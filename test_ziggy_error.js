// Test script to reproduce the Ziggy routing error
console.log('Testing Ziggy route error...');

// Simulate the problematic route call from line 36
const procedureLink = 'https://politiq.app/my-procedures/1/saepe-officia-illum-qui';

try {
    // This should simulate what happens on line 36 in myProcedures.vue
    console.log('Attempting to use route() with:', procedureLink);
    // route(procedureLink) - this would cause the Ziggy error
    console.log('Error: <PERSON>ig<PERSON> cannot find route:', procedureLink);
    console.log('Expected route name should be: myProcedureDetails');
    console.log('Expected parameters: {id: 1, slug: "saepe-officia-illum-qui"}');
} catch (error) {
    console.error('Ziggy error:', error.message);
}
