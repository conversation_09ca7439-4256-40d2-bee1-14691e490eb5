# Abstract ChoicesSelect Component Documentation

## Overview

The ChoicesSelect component has been enhanced to support abstract API endpoints and flexible data mapping. This allows you to use the same component with different models and controllers.

## New Features

### 1. **searchConfig Property**
Replace the old `searchRoute` with a flexible `searchConfig` object:

```typescript
interface ApiSearchConfig {
    endpoint: string;                    // API endpoint URL
    searchParam?: string;               // Parameter name for search term (default: 'searchKey')
    valueField?: string;                // Field to use as option value (default: 'id')
    labelField?: string;                // Field to use as option label (default: 'name')
    dataPath?: string;                  // Path to data in response (default: 'data.data')
    additionalParams?: Record<string, any>; // Extra parameters to send
    minSearchLength?: number;           // Minimum search length (default: 2)
    resultsPerPage?: number;           // Results per page (default: 50)
}
```

### 2. **useApiSearch Composable**
A reusable composable for making API search requests:

```typescript
import { useApiSearch } from '@/composables/useApiSearch';

const { search } = useApiSearch();
const results = await search('john', {
    endpoint: '/api/users/filter',
    valueField: 'id',
    labelField: 'name'
});
```

## Usage Examples

### Basic User Search
```vue
<ChoicesSelect
    id="user-select"
    v-model="selectedUser"
    label="Select User"
    :search-config="{
        endpoint: '/api/users/filter',
        searchParam: 'searchKey',
        valueField: 'id',
        labelField: 'name',
        dataPath: 'data.data'
    }"
/>
```

### User Groups with Custom Label
```vue
<ChoicesSelect
    id="usergroup-select"
    v-model="selectedUserGroup"
    label="Select User Group"
    :search-config="{
        endpoint: '/api/user-groups/filter',
        valueField: 'id',
        labelField: 'title',
        additionalParams: {
            with: ['admin', 'users']
        }
    }"
/>
```

### Tags with System Filter
```vue
<ChoicesSelect
    id="tags-select"
    v-model="selectedTags"
    label="Select Tags"
    :multiple="true"
    :search-config="{
        endpoint: '/api/tags/filter',
        valueField: 'id',
        labelField: 'title',
        additionalParams: {
            system: false
        }
    }"
/>
```

### Procedures with Active Filter
```vue
<ChoicesSelect
    id="procedures-select"
    v-model="selectedProcedure"
    label="Select Procedure"
    :search-config="{
        endpoint: '/api/procedures/filter',
        valueField: 'id',
        labelField: 'title',
        additionalParams: {
            activePeriod: '1'
        }
    }"
/>
```

### Custom API Structure
```vue
<ChoicesSelect
    id="custom-select"
    v-model="selectedItem"
    label="Select Item"
    :search-config="{
        endpoint: '/api/external/search',
        searchParam: 'query',
        valueField: 'uuid',
        labelField: 'display_name',
        dataPath: 'results.items',
        minSearchLength: 3,
        additionalParams: {
            type: 'active',
            sort: 'name'
        }
    }"
/>
```

## Required Backend Endpoints

To use the abstract ChoicesSelect, you need to create API endpoints that return JSON in this format:

```json
{
    "success": true,
    "data": {
        "data": [
            {
                "id": 1,
                "name": "John Doe",
                "email": "<EMAIL>"
            }
        ],
        "current_page": 1,
        "per_page": 20,
        "total": 100
    },
    "message": "Results retrieved successfully"
}
```

### Example Controller Methods

#### UserGroupController
```php
public function indexJson(Request $request): JsonResponse
{
    $userGroupRequestDTO = new UserGroupRequestDTO();
    $userGroupRequestDTO->searchKey = $request->input('searchKey', '');
    $userGroupRequestDTO->with = $request->input('with', []);
    $userGroupRequestDTO->rpp = $request->input('rpp', 20);
    
    $userGroups = $this->userGroupService->getUserGroups($userGroupRequestDTO);
    
    return response()->json([
        'success' => true,
        'data' => $userGroups,
        'message' => 'User groups retrieved successfully'
    ]);
}
```

#### TagController
```php
public function indexJson(Request $request): JsonResponse
{
    $tagRequestDTO = new TagRequestDTO();
    $tagRequestDTO->searchKey = $request->input('searchKey', '');
    $tagRequestDTO->system = $request->boolean('system');
    $tagRequestDTO->rpp = $request->input('rpp', 20);
    
    $tags = $this->tagService->getTags($tagRequestDTO);
    
    return response()->json([
        'success' => true,
        'data' => $tags,
        'message' => 'Tags retrieved successfully'
    ]);
}
```

#### ProcedureController
```php
public function indexJson(Request $request): JsonResponse
{
    $procedureRequestDTO = new ProcedureRequestDTO();
    $procedureRequestDTO->searchKey = $request->input('searchKey', '');
    $procedureRequestDTO->activePeriod = $request->input('activePeriod');
    $procedureRequestDTO->rpp = $request->input('rpp', 20);
    
    $procedures = $this->procedureService->getProcedures($procedureRequestDTO);
    
    return response()->json([
        'success' => true,
        'data' => $procedures,
        'message' => 'Procedures retrieved successfully'
    ]);
}
```

### Required Routes
Add these routes outside the Inertia middleware group:

```php
// API routes without Inertia middleware (for JSON responses)
Route::middleware(['auth', CheckUserActive::class, LanguageMiddleware::class])
    ->group(function () {
        Route::post('/api/users/filter', [UserController::class, 'indexJson'])
            ->name('userIndexJson');
        Route::post('/api/user-groups/filter', [UserGroupController::class, 'indexJson'])
            ->name('userGroupIndexJson');
        Route::post('/api/tags/filter', [TagController::class, 'indexJson'])
            ->name('tagIndexJson');
        Route::post('/api/procedures/filter', [ProcedureController::class, 'indexJson'])
            ->name('procedureIndexJson');
    });
```

## Predefined Configurations

The composable includes predefined configurations for common use cases:

```typescript
import { searchConfigs, useUserSearch, useTagSearch } from '@/composables/useApiSearch';

// Use predefined configs
const userConfig = searchConfigs.users;
const tagConfig = searchConfigs.tags;

// Or use type-safe search functions
const searchUsers = useUserSearch();
const searchTags = useTagSearch();

const users = await searchUsers('john');
const tags = await searchTags('important');
```

## Migration Guide

### From Old searchRoute
```vue
<!-- Old way -->
<ChoicesSelect
    search-route="userIndexPost"
    ...
/>

<!-- New way -->
<ChoicesSelect
    :search-config="{
        endpoint: '/api/users/filter',
        valueField: 'id',
        labelField: 'name'
    }"
    ...
/>
```

### Benefits of New Approach
1. **Flexible**: Works with any API endpoint
2. **Type-safe**: Full TypeScript support
3. **Reusable**: Same component for different models
4. **Configurable**: Custom field mappings and parameters
5. **Backward compatible**: Old searchRoute still works

## Advanced Usage

### Custom Data Transformation
```typescript
const customConfig = {
    endpoint: '/api/complex/search',
    valueField: 'metadata.id',
    labelField: 'profile.display_name',
    dataPath: 'response.results.items',
    additionalParams: {
        include: ['profile', 'metadata'],
        filter: 'active'
    }
};
```

### Multiple Field Labels
```typescript
// For complex label formatting, you can process results after search
const { search } = useApiSearch();
const results = await search(term, config);

const processedResults = results.map(result => ({
    ...result,
    label: `${result.original.name} (${result.original.email})`
}));
```

This abstract approach makes the ChoicesSelect component extremely flexible and reusable across your entire application!
