# User Filtering API Documentation

## Overview

The UserController now provides multiple ways to filter and retrieve user data:

1. **Original `indexPost`** - Stores search in session and redirects (unchanged)
2. **New `indexJson`** - Dedicated JSON endpoint for filtering
3. **New `indexPostFlexible`** - Smart endpoint that returns JSON for AJAX or redirects for regular forms

## Endpoints

### 1. `/api/users/filter` (POST) - JSON Only
**Route Name:** `userIndexJson`
**Method:** `indexJson`
**Returns:** Always JSON

### 2. `/users/flexible` (POST) - Smart Endpoint  
**Route Name:** `userIndexFlexible`
**Method:** `indexPostFlexible`
**Returns:** JSON for AJAX requests, redirect for regular forms

### 3. `/users` (POST) - Original Endpoint
**Route Name:** `userIndexPost`
**Method:** `indexPost`
**Returns:** Always redirect

## Request Parameters

All endpoints accept the following parameters:

| Parameter | Type | Description | Default |
|-----------|------|-------------|---------|
| `searchKey` | string | Search in name, email, surname | `''` |
| `isActive` | boolean | Filter by active status | `null` |
| `status` | string | Filter by status ('active', etc.) | `null` |
| `roles` | array | Filter by user roles | `[]` |
| `rpp` | integer | Results per page | `20` |
| `all` | boolean | Return all results (no pagination) | `false` |
| `orderBy` | array | Order by fields | `[]` |

## Response Format (JSON endpoints)

```json
{
    "success": true,
    "data": {
        "data": [...], // User objects
        "current_page": 1,
        "per_page": 20,
        "total": 100,
        "links": [...]
    },
    "message": "Users retrieved successfully"
}
```

## Usage Examples

### JavaScript/Fetch API

```javascript
// Basic search
const response = await fetch('/api/users/filter', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    },
    body: JSON.stringify({
        searchKey: 'john',
        isActive: true,
        rpp: 10
    })
});

const data = await response.json();
console.log(data.data); // User results
```

### Vue 3 with Composition API

```javascript
import { ref, reactive } from 'vue';

export function useUserFiltering() {
    const users = ref([]);
    const loading = ref(false);
    
    const filters = reactive({
        searchKey: '',
        isActive: null,
        rpp: 20
    });

    const fetchUsers = async () => {
        loading.value = true;
        try {
            const response = await fetch('/api/users/filter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(filters)
            });
            
            const data = await response.json();
            users.value = data.data;
        } catch (error) {
            console.error('Error:', error);
        } finally {
            loading.value = false;
        }
    };

    return { users, loading, filters, fetchUsers };
}
```

### Axios

```javascript
// With Axios
const response = await axios.post('/api/users/filter', {
    searchKey: 'smith',
    isActive: true,
    roles: ['admin'],
    rpp: 15
});

console.log(response.data);
```

### jQuery

```javascript
$.ajaxSetup({
    headers: {
        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    }
});

$.post('/api/users/filter', {
    searchKey: 'test',
    isActive: true
})
.done(function(data) {
    console.log('Users:', data.data);
});
```

### Inertia.js

```javascript
import { router } from '@inertiajs/vue3';

// For the flexible endpoint
router.post('/users/flexible', 
    { searchKey: 'john', rpp: 10 },
    {
        preserveState: true,
        onSuccess: (page) => {
            // Handle JSON response
            console.log(page.props);
        }
    }
);
```

## Advanced Filtering

### Multiple Roles
```javascript
{
    "roles": ["admin", "moderator", "user"],
    "searchKey": "john"
}
```

### Custom Ordering
```javascript
{
    "orderBy": ["name", "email"],
    "searchKey": "smith"
}
```

### Get All Results (No Pagination)
```javascript
{
    "all": true,
    "isActive": true
}
```

## Error Handling

```javascript
try {
    const response = await fetch('/api/users/filter', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(filters)
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    if (!data.success) {
        throw new Error(data.message || 'Failed to fetch users');
    }

    return data.data;
} catch (error) {
    console.error('Error fetching users:', error);
    // Handle error appropriately
}
```

## Migration Guide

### From Original indexPost

**Before:**
```javascript
// Only worked with form submissions and redirects
<form method="POST" action="/users">
    <input name="searchKey" value="john">
    <button type="submit">Search</button>
</form>
```

**After (Option 1 - Use JSON endpoint):**
```javascript
// AJAX call for dynamic filtering
const users = await fetch('/api/users/filter', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ searchKey: 'john' })
});
```

**After (Option 2 - Use flexible endpoint):**
```javascript
// Works with both AJAX and form submissions
// AJAX request (returns JSON)
fetch('/users/flexible', {
    method: 'POST',
    headers: { 
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    body: JSON.stringify({ searchKey: 'john' })
});

// Form submission (redirects)
<form method="POST" action="/users/flexible">
    <input name="searchKey" value="john">
    <button type="submit">Search</button>
</form>
```

## Best Practices

1. **Use the dedicated JSON endpoint** (`/api/users/filter`) for pure API calls
2. **Use the flexible endpoint** (`/users/flexible`) when you need to support both AJAX and regular form submissions
3. **Always include CSRF token** for POST requests
4. **Implement proper error handling** for network and API errors
5. **Use debouncing** for real-time search to avoid excessive API calls
6. **Cache results** when appropriate to improve performance

## Security Notes

- All endpoints require authentication (protected by auth middleware)
- CSRF protection is enforced for all POST requests
- Input validation is handled by the UserRequestDTO
- No sensitive user data is exposed in the JSON responses
