# Politiq Development Guide

![Company logo](./resources/images/logo.png)

Welcome to the **Politiq** development environment documentation.  
This guide provides quick setup instructions, useful commands, and references for developers.

---

## 📑 Table of Contents

1. [Development Instructions](#️-development-instructions)
    - [Local Environment](#local-environment)
    - [Common Commands](#common-commands)
    - [Kill a Process on Port 5174](#kill-a-process-on-port-5174)
2. [External Resources](#-external-resources)
3. [Developer Contacts](#-developer-contacts)

---

## ⚙️ Development Instructions

### Local Environment

- **Local URL:** [https://politiq.app](https://politiq.app)

### Common Commands

- **Generate JSON language files:**
  ```bash
  php artisan lang:export-json
  ```
- **Run fresh migrations with seed data:**
  ```bash
  php artisan migrate:fresh --seed
  ```

### Kill a Process on Port `5174`

If Vite is stuck or the dev server doesn’t start, free the port:

```bash
sudo lsof -i :5174
sudo kill <PID>
```

Replace `<PID>` with the process ID from the `lsof` command.

---

## 📌 External Resources

- **Project Board:** [Trello](https://trello.com/b/8Ef6ZPPu/politiq)

---

## 👨‍💻 Developer Contacts

- **Alex Banagos** – [<EMAIL>](mailto:<EMAIL>)
- **Panagiotis Gkatzelidis** – [<EMAIL>](mailto:<EMAIL>)
- **Simos Fasouliotis** – [<EMAIL>](mailto:<EMAIL>)

---

> 📝 *For any issues, please reach out to the listed contacts.*
