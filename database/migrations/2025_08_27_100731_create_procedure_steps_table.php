<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('procedure_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('procedureId')
                ->constrained('procedures')
                ->cascadeOnDelete();
            $table->enum('type', ['discussion', 'poll', 'survey']);
            $table->dateTime('starts_at');
            $table->dateTime('ends_at');
            $table->mediumText('description')
                ->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('procedure_steps');
    }
};
