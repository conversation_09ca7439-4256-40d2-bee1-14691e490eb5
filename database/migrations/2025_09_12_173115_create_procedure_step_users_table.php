<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('procedure_step_users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('procedureStepId')
                ->constrained('procedure_steps')
                ->cascadeOnDelete();
            $table->foreignId('userId')
                ->constrained('users')
                ->cascadeOnDelete();
            $table->timestamps();

            $table->unique(['procedureStepId', 'userId']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('procedure_step_users');
    }
};
