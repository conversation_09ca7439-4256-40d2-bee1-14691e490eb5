<?php

namespace Database\Seeders;

use App\Models\Tag;
use App\Models\User;
use App\Models\UserGroup;
use Illuminate\Database\Seeder;

class UserGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        $tags = Tag::all();

        UserGroup::factory(15)
            ->create()
            ->each(function (UserGroup $group) use ($users, $tags) {

                // Pick a random (unique) set of IDs
                $tagIds = $tags->pluck('id')
                    ->shuffle()
                    ->take(fake()->numberBetween(1, 5))
                    ->all();
                $userIds = $users->pluck('id')
                    ->shuffle()
                    ->take(fake()->numberBetween(1, 3))
                    ->all();

                // Attach to pivots. Your custom keys (userGroupId/tagId/userId) are
                // already handled by the relationships you defined.
                $group->tags()
                    ->attach($tagIds);   // withTimestamps() will set pivot timestamps
                $group->users()
                    ->attach($userIds);
            });
    }
}
