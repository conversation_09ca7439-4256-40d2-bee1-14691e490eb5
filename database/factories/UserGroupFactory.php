<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\UserGroup>
 */
class UserGroupFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title'       => $this->faker->unique()
                ->words(3, true),
            'description' => $this->faker->paragraphs(mt_rand(2, 4), true),
            'adminId'     => $this->faker->numberBetween(1, 3),
            'isDeleted'   => false,
        ];
    }
}
