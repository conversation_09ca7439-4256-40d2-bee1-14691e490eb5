<?php

namespace Database\Factories;

use App\Models\ProcedureStep;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProcedureStepUser>
 */
class ProcedureStepUserFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'procedureStepId' => ProcedureStep::query()
                                     ->inRandomOrder()
                                     ->value('id') ?? ProcedureStep::factory(),
            'userId'          => User::query()
                                     ->inRandomOrder()
                                     ->value('id') ?? User::factory(),
        ];
    }
}
