<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProcedureStep>
 */
class ProcedureStepFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $now = Carbon::now();
        $startDate = Carbon::instance(
            fake()->dateTimeBetween(
                $now->copy()
                    ->subMonths(6),
                $now->copy()
                    ->addMonths(6)
            )
        );

        return [
            'type'        => fake()->randomElement(config('procedures.steps.types')),
            'starts_at'   => $startDate,
            'ends_at'     => Carbon::parse($startDate)
                ->addDay(),
            'description' => fake()->sentences(1, true),
        ];
    }
}
