<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProcedureController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\ThreadController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserGroupController;
use App\Http\Middleware\CheckUserActive;
use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\LanguageMiddleware;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;

$language = config('languages.languages')[0]['code']; // default

foreach (config('languages.languages') as $lang) {
    if((request()->segment(1) ?? '') === $lang['code']) {
        App::setLocale($lang['code']);
    }
}

Route::middleware([
    LanguageMiddleware::class,
    HandleInertiaRequests::class
])
    ->group(
        function () {
            Route::get('testpage', [TestController::class, 'testpage'])
                ->name('testpage');

            // USER
            Route::get(trans('routes.login.furl'), [AuthController::class, 'login'])
                ->name('login');
            Route::post(trans('routes.login.furl'), [AuthController::class, 'loginPost'])
                ->name('loginPost');
            Route::get(trans('routes.register.furl'), [UserController::class, 'register'])
                ->name('register');
            Route::post(trans('routes.register.furl'), [UserController::class, 'registerPost'])
                ->name('registerPost');
            Route::get(trans('routes.pendingConfirmation.furl'), [AuthController::class, 'pendingConfirmation'])
                ->name('pendingConfirmation');



            // FORGOT PASSWORD
            Route::get(trans('routes.forgotPassword.furl'), [AuthController::class, 'forgotPassword'])
                ->name('forgotPassword');
            Route::get(trans('routes.resetPassword.furl'), [AuthController::class, 'resetPassword'])
                ->name('resetPassword');
        }
    );

Route::middleware([
    'auth',
    CheckUserActive::class,
    LanguageMiddleware::class,
    HandleInertiaRequests::class
])
    ->group(
        function () {
            Route::get(trans('routes.dashboard.furl'), [DashboardController::class, 'dashboard'])
                ->name('dashboard');

            // TAGS
            Route::get(trans('routes.tagIndex.furl'), [TagController::class, 'index'])
                ->name('tagIndex');
            Route::post(trans('routes.tagIndex.furl'), [TagController::class, 'indexPost'])
                ->name('tagIndexPost');
            Route::get(trans('routes.tagAdd.furl'), [TagController::class, 'addTag'])
                ->name('tagAdd');
            Route::post(trans('routes.tagAdd.furl'), [TagController::class, 'addTagPost'])
                ->name('addTagPost');
            Route::delete(trans('routes.tagDelete.furl'), [TagController::class, 'deleteTag'])
                ->where('id', '[0-9]+')
                ->name('deleteTag');
            Route::delete(trans('routes.massDeleteTags.furl'), [TagController::class, 'massDeleteTags'])->name('massDeleteTags');
            Route::get(trans('routes.tagUpdate.furl'), [TagController::class, 'updateTag'])
                ->where('id', '[0-9]+')
                ->name('updateTag');
            Route::post(trans('routes.tagUpdate.furl'), [TagController::class, 'updateTagPost'])
                ->where('id', '[0-9]+')
                ->name('updateTagPost');

            // USER GROUPS
            Route::get(trans('routes.userGroupIndex.furl'), [UserGroupController::class, 'index'])
                ->name('userGroupIndex');
            Route::post(trans('routes.userGroupIndex.furl'), [UserGroupController::class, 'indexPost'])
                ->name('userGroupIndexPost');
            Route::get(trans('routes.userGroupAdd.furl'), [UserGroupController::class, 'addUserGroup'])
                ->name('userGroupAdd');
            Route::post(trans('routes.userGroupAdd.furl'), [UserGroupController::class, 'addUserGroupPost'])
                ->name('addUserGroupPost');
            Route::delete('/userGroups/{id}', [UserGroupController::class, 'deleteUserGroup'])
                ->where('id', '[0-9]+')
                ->name('deleteUserGroup');
            Route::get(trans('routes.userGroupUpdate.furl'), [UserGroupController::class, 'updateUserGroup'])
                ->where('id', '[0-9]+')
                ->name('updateUserGroup');
            Route::post(trans('routes.userGroupUpdate.furl'), [UserGroupController::class, 'updateUserGroupPost'])
                ->where('id', '[0-9]+')
                ->name('updateUserGroupPost');


            Route::get(trans('routes.userIndex.furl'), [UserController::class, 'index'])
                ->name('userIndex');
            Route::post(trans('routes.userIndex.furl'), [UserController::class, 'indexPost'])
                ->name('userIndexPost');
            Route::post('/api/users/filter', [UserController::class, 'indexJson'])
                ->name('userIndexJson');

            // AUTH
            Route::get(trans('routes.logoff.furl'), [AuthController::class, 'logoff'])
                ->name('logoff');

            // PROCEDURES
            Route::get(trans('routes.procedureIndex.furl'), [ProcedureController::class, 'index'])
                ->name('procedureIndex');
            Route::post(trans('routes.procedureIndex.furl'), [ProcedureController::class, 'indexPost'])
                ->name('procedureIndexPost');
            Route::get(trans('routes.procedureAdd.furl'), [ProcedureController::class, 'addProcedure'])
                ->name('procedureAdd');
            Route::post(trans('routes.procedureAdd.furl'), [ProcedureController::class, 'addProcedurePost'])
                ->name('addProcedurePost');
            Route::delete(trans('routes.procedureDelete.furl'), [ProcedureController::class, 'deleteProcedure'])
                ->where('id', '[0-9]+')
                ->name('deleteProcedure');
            Route::delete(trans('routes.massDeleteProcedures.furl'), [ProcedureController::class, 'massDeleteProcedures'])
                ->name('massDeleteProcedures');
            Route::get(trans('routes.procedureUpdate.furl'), [ProcedureController::class, 'updateProcedure'])
                ->where('id', '[0-9]+')
                ->name('updateProcedure');
            Route::post(trans('routes.procedureUpdate.furl'), [ProcedureController::class, 'updateProcedurePost'])
                ->where('id', '[0-9]+')
                ->name('updateProcedurePost');

            Route::get(trans('routes.myProcedures.furl'), [ProcedureController::class, 'myProcedures'])
                ->name('myProcedures');
            Route::get(trans('routes.myProcedureDetails.furl'), [ProcedureController::class, 'myProcedureDetails'])
                ->where('id', '[0-9]+')
                ->name('myProcedureDetails');
            Route::get(trans('routes.myProcedureStepDetails.furl'), [ProcedureController::class, 'myProcedureStepDetails'])
                ->where('id', '[0-9]+')
                ->where('procedureI', '[0-9]+')
                ->name('myProcedureStepDetails');

            // THREADS
            Route::post(trans('routes.threadAdd.furl'), [ThreadController::class, 'addThreadPost'])
                ->name('addThreadPost');
            Route::delete(trans('routes.threadDelete.furl'), [ThreadController::class, 'deleteThread'])
                ->where('id', '[0-9]+')
                ->name('deleteThread');
            Route::post(trans('routes.threadUpdate.furl'), [ThreadController::class, 'updateThreadPost'])
                ->where('id', '[0-9]+')
                ->name('updateThreadPost');
        }
    );
