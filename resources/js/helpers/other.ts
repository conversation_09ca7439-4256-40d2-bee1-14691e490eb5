type ToggleAttributeProps = (attribute: string, value: string, tag?: keyof HTMLElementTagNameMap, remove?: boolean) => void;

export const toggleDocumentAttribute: ToggleAttributeProps = (attribute, value, tag = 'html', remove): void => {
    // if (!document.body) return
    const element = document.getElementsByTagName(tag)[0];
    const hasAttribute = element?.hasAttribute(attribute);
    if (remove && hasAttribute) {
        element?.removeAttribute(attribute);
    } else element?.setAttribute(attribute, value);
};

/**
 * Format date string to DD/MM/YYYY format
 * @param dateString - Date string to format
 * @returns Formatted date string or original string if invalid
 */
export const formatDate = (dateString: string | null | undefined): string => {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
    } catch (error) {
        return dateString;
    }
};
/**
 * Format date string to DD/MM/YYYY HH:MM format
 * @param dateString - Date string to format
 * @returns Formatted date string with time or original string if invalid
 */
export const formatDateTime = (dateString: string | null | undefined): string => {
    if (!dateString) return '';

    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return dateString;

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}`;
    } catch (error) {
        return dateString;
    }
};
