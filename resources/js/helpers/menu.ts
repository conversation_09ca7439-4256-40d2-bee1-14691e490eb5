import {MenuType} from '@/types/layout';

export const menu: MenuType[] = [
    {
        label: 'menu.dashboard',
        key: 'dashboardSection',
        isTitle: true,
        icon: 'ti ti-dashboard',
        children: [
            {
                label: 'menu.dashboard',
                key: 'dashboard',
                url: 'dashboard',
                icon: 'ti ti-dashboard',
            },
            {
                label: 'menu.users',
                key: 'users',
                url: 'userIndex',
                icon: 'ti ti-users',
            },
            {
                label: 'menu.tags',
                key: 'tags',
                url: 'tagIndex',
                icon: 'ti ti-tags',
            },
            {
                label: 'menu.userGroups',
                key: 'userGroups',
                url: 'userGroupIndex',
                icon: 'ti ti-users-group',
            },
            {
                label: 'menu.procedures',
                key: 'procedures',
                url: 'procedureIndex',
                icon: 'ti ti-bubble',
            },
        ],
    },
    {
        label: 'User Menu',
        key: 'userMenu',
        isTitle: true,
        icon: 'ti ti-dashboard',
        children: [
            {
                label: 'menu.myProcedures',
                key: 'procedures',
                url: 'myProcedures',
                icon: 'ti ti-bubble',
            },
        ],
    },
    {
        label: 'Tests',
        key: 'tests',
        isTitle: true,
        icon: 'ti ti-dashboard',
        children: [
            {
                url: 'testpage',
                label: 'Test UserGroup Details',
                icon: 'ti ti-wallet',
                parentKey: 'tests',
                tooltip: {
                    variant: 'danger',
                    icon: 'ti ti-info-triangle',
                    text: 'Your menu item <b>tooltip!</b>',
                },
            },
        ],
    },
];
