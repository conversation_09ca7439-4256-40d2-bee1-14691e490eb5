<template>
    <div class="choices-select-example p-4">
        <h3>ChoicesSelect with Object Arrays Example</h3>
        
        <!-- Example Data Display -->
        <div class="example-data mb-4 p-3 bg-light border rounded">
            <h5>Current Data Structure:</h5>
            <pre>{{ JSON.stringify(exampleStep, null, 2) }}</pre>
        </div>

        <!-- ChoicesSelect Component -->
        <div class="choices-example mb-4">
            <h5>ChoicesSelect Component:</h5>
            <ChoicesSelect
                id="example-users"
                label="Assigned Users"
                :options="getStepUserOptions(exampleStep)"
                v-model="getStepUserIds(exampleStep).value"
                :search-config="{
                    endpoint: '/api/users/filter',
                    searchParam: 'searchKey',
                    valueField: 'id',
                    labelField: 'name',
                    dataPath: 'data.data',
                    additionalParams: { isActive: true, rpp: 30 }
                }"
                :choice-options="{ 
                    addItems: false, 
                    removeItemButton: true, 
                    duplicateItemsAllowed: false, 
                    paste: false 
                }"
                :multiple="true"
                placeholder="Search and select users..."
            />
        </div>

        <!-- Current Selection Display -->
        <div class="selection-display p-3 bg-success bg-opacity-10 border border-success rounded">
            <h5 class="text-success">Current Selection:</h5>
            <p><strong>Selected User IDs:</strong> {{ getStepUserIds(exampleStep).value }}</p>
            <p><strong>Selected User Names:</strong> {{ getSelectedUserNames() }}</p>
        </div>

        <!-- Action Buttons -->
        <div class="actions mt-4">
            <button @click="addRandomUser" class="btn btn-primary me-2">Add Random User</button>
            <button @click="clearSelection" class="btn btn-secondary me-2">Clear Selection</button>
            <button @click="logCurrentState" class="btn btn-info">Log Current State</button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import ChoicesSelect from '@/components/ChoicesSelect.vue';

// Example step data (similar to your procedureStep)
const exampleStep = ref({
    id: 1,
    title: "Review Documents",
    users: [
        { id: 1, name: "John Doe", email: "<EMAIL>" },
        { id: 3, name: "Alice Johnson", email: "<EMAIL>" }
    ]
});

// Available users for testing
const availableUsers = [
    { id: 1, name: "John Doe", email: "<EMAIL>" },
    { id: 2, name: "Jane Smith", email: "<EMAIL>" },
    { id: 3, name: "Alice Johnson", email: "<EMAIL>" },
    { id: 4, name: "Bob Wilson", email: "<EMAIL>" },
    { id: 5, name: "Carol Brown", email: "<EMAIL>" }
];

// Helper to get user IDs for v-model
const getStepUserIds = (step: any) => {
    return computed({
        get: () => step.users?.map((user: any) => user.id) || [],
        set: (newUserIds: number[]) => {
            console.log(`Step ${step.id} users updated:`, newUserIds);
            
            // Update the step.users array with full user objects
            step.users = availableUsers.filter(user => newUserIds.includes(user.id));
            
            // Here you could also make an API call to save the changes
            // await saveStepUsers(step.id, newUserIds);
        }
    });
};

// Helper to get initial options for the select
const getStepUserOptions = (step: any) => {
    return step.users?.map((user: any) => ({ 
        value: user.id, 
        text: user.name 
    })) || [];
};

// Helper to get selected user names for display
const getSelectedUserNames = () => {
    return exampleStep.value.users?.map(user => user.name).join(', ') || 'None';
};

// Action methods
const addRandomUser = () => {
    const unselectedUsers = availableUsers.filter(user => 
        !exampleStep.value.users.some(selectedUser => selectedUser.id === user.id)
    );
    
    if (unselectedUsers.length > 0) {
        const randomUser = unselectedUsers[Math.floor(Math.random() * unselectedUsers.length)];
        exampleStep.value.users.push(randomUser);
    }
};

const clearSelection = () => {
    exampleStep.value.users = [];
};

const logCurrentState = () => {
    console.log('Current step state:', exampleStep.value);
    console.log('Selected user IDs:', getStepUserIds(exampleStep.value).value);
    console.log('Selected user names:', getSelectedUserNames());
};
</script>

<style scoped>
.choices-select-example {
    max-width: 800px;
    margin: 0 auto;
}

.example-data pre {
    max-height: 200px;
    overflow-y: auto;
    font-size: 0.8rem;
}

h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

h5 {
    color: #666;
    margin-bottom: 0.5rem;
}

.actions button {
    margin-bottom: 0.5rem;
}
</style>
