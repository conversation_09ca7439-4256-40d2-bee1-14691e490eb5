<template>
    <div class="procedure-details-example">
        <h3>Procedure Details - ChoicesSelect Examples</h3>
        
        <!-- Example: Assign Users to Procedure Step -->
        <div class="form-group mb-4">
            <ChoicesSelect
                id="assign-users"
                v-model="assignedUsers"
                label="Assign Users to Step"
                :search-config="userSearchConfig"
                :multiple="true"
                placeholder="Search and select users..."
            />
        </div>

        <!-- Example: Assign User Groups to Procedure Step -->
        <div class="form-group mb-4">
            <ChoicesSelect
                id="assign-user-groups"
                v-model="assignedUserGroups"
                label="Assign User Groups to Step"
                :search-config="userGroupSearchConfig"
                :multiple="true"
                placeholder="Search and select user groups..."
            />
        </div>

        <!-- Example: Add Tags to Procedure -->
        <div class="form-group mb-4">
            <ChoicesSelect
                id="procedure-tags"
                v-model="procedureTags"
                label="Procedure Tags"
                :search-config="tagSearchConfig"
                :multiple="true"
                placeholder="Search and select tags..."
            />
        </div>

        <!-- Example: Link Related Procedures -->
        <div class="form-group mb-4">
            <ChoicesSelect
                id="related-procedures"
                v-model="relatedProcedures"
                label="Related Procedures"
                :search-config="procedureSearchConfig"
                :multiple="true"
                placeholder="Search and select related procedures..."
            />
        </div>

        <!-- Display selected values -->
        <div class="selected-values mt-4">
            <h4>Selected Values:</h4>
            <div class="row">
                <div class="col-md-6">
                    <strong>Assigned Users:</strong>
                    <pre>{{ JSON.stringify(assignedUsers, null, 2) }}</pre>
                </div>
                <div class="col-md-6">
                    <strong>Assigned User Groups:</strong>
                    <pre>{{ JSON.stringify(assignedUserGroups, null, 2) }}</pre>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <strong>Procedure Tags:</strong>
                    <pre>{{ JSON.stringify(procedureTags, null, 2) }}</pre>
                </div>
                <div class="col-md-6">
                    <strong>Related Procedures:</strong>
                    <pre>{{ JSON.stringify(relatedProcedures, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ChoicesSelect from '@/components/ChoicesSelect.vue';
import type { ApiSearchConfig } from '@/composables/useApiSearch';

// Props that would come from the parent component
const props = defineProps<{
    procedureId?: number;
    stepId?: number;
}>();

// Reactive values for selections
const assignedUsers = ref<number[]>([]);
const assignedUserGroups = ref<number[]>([]);
const procedureTags = ref<number[]>([]);
const relatedProcedures = ref<number[]>([]);

// Search configurations for different entities
const userSearchConfig: ApiSearchConfig = {
    endpoint: '/api/users/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'name',
    dataPath: 'data.data',
    additionalParams: {
        isActive: true, // Only active users
        rpp: 30
    }
};

const userGroupSearchConfig: ApiSearchConfig = {
    endpoint: '/api/user-groups/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        with: ['admin'], // Include admin info
        rpp: 20
    }
};

const tagSearchConfig: ApiSearchConfig = {
    endpoint: '/api/tags/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        system: false, // Exclude system tags
        rpp: 25
    }
};

const procedureSearchConfig: ApiSearchConfig = {
    endpoint: '/api/procedures/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        activePeriod: '1', // Only active procedures
        excludeId: props.procedureId, // Exclude current procedure
        rpp: 20
    }
};

// Example methods for handling the selections
const saveAssignments = async () => {
    const payload = {
        stepId: props.stepId,
        userIds: assignedUsers.value,
        userGroupIds: assignedUserGroups.value,
        tagIds: procedureTags.value,
        relatedProcedureIds: relatedProcedures.value
    };

    try {
        // Make API call to save assignments
        const response = await fetch('/api/procedure-steps/assign', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(payload)
        });

        if (response.ok) {
            console.log('Assignments saved successfully');
        }
    } catch (error) {
        console.error('Error saving assignments:', error);
    }
};

// Watch for changes and auto-save (optional)
// watch([assignedUsers, assignedUserGroups, procedureTags, relatedProcedures], () => {
//     saveAssignments();
// }, { deep: true });
</script>

<style scoped>
.procedure-details-example {
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
}

.form-group {
    margin-bottom: 1.5rem;
}

.selected-values {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

pre {
    background: #ffffff;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    font-size: 0.8rem;
    max-height: 150px;
    overflow-y: auto;
}

h3 {
    color: #333;
    margin-bottom: 2rem;
}

h4 {
    color: #666;
    margin-bottom: 1rem;
}
</style>
