<template>
    <div class="choices-select-examples">
        <h3>ChoicesSelect Component Usage Examples</h3>

        <!-- Example 1: Users with new searchConfig -->
        <div class="mb-4">
            <h4>1. Users Search (New API)</h4>
            <ChoicesSelect
                id="users-select"
                v-model="selectedUser"
                label="Select User"
                :search-config="userSearchConfig"
                :multiple="false"
            />
            <p class="text-muted">Selected User ID: {{ selectedUser }}</p>
        </div>

        <!-- Example 2: User Groups -->
        <div class="mb-4">
            <h4>2. User Groups Search</h4>
            <ChoicesSelect
                id="usergroups-select"
                v-model="selectedUserGroup"
                label="Select User Group"
                :search-config="userGroupSearchConfig"
                :multiple="false"
            />
            <p class="text-muted">Selected User Group ID: {{ selectedUserGroup }}</p>
        </div>

        <!-- Example 3: Tags with Multiple Selection -->
        <div class="mb-4">
            <h4>3. Tags Search (Multiple)</h4>
            <ChoicesSelect
                id="tags-select"
                v-model="selectedTags"
                label="Select Tags"
                :search-config="tagSearchConfig"
                :multiple="true"
            />
            <p class="text-muted">Selected Tag IDs: {{ selectedTags }}</p>
        </div>

        <!-- Example 4: Procedures -->
        <div class="mb-4">
            <h4>4. Procedures Search</h4>
            <ChoicesSelect
                id="procedures-select"
                v-model="selectedProcedure"
                label="Select Procedure"
                :search-config="procedureSearchConfig"
                :multiple="false"
            />
            <p class="text-muted">Selected Procedure ID: {{ selectedProcedure }}</p>
        </div>

        <!-- Example 5: Custom API with different structure -->
        <div class="mb-4">
            <h4>5. Custom API Example</h4>
            <ChoicesSelect
                id="custom-select"
                v-model="selectedCustom"
                label="Select Custom Item"
                :search-config="customSearchConfig"
                :multiple="false"
            />
            <p class="text-muted">Selected Custom ID: {{ selectedCustom }}</p>
        </div>

        <!-- Example 6: Legacy searchRoute (backward compatibility) -->
        <div class="mb-4">
            <h4>6. Legacy searchRoute (Backward Compatibility)</h4>
            <ChoicesSelect
                id="legacy-select"
                v-model="selectedLegacy"
                label="Select User (Legacy)"
                search-route="userIndexPost"
                :multiple="false"
            />
            <p class="text-muted">Selected Legacy User ID: {{ selectedLegacy }}</p>
        </div>

        <!-- Example 7: With predefined options -->
        <div class="mb-4">
            <h4>7. With Predefined Options + Search</h4>
            <ChoicesSelect
                id="predefined-select"
                v-model="selectedPredefined"
                label="Select User (with predefined options)"
                :search-config="userSearchConfig"
                :options="predefinedUsers"
                :multiple="false"
            />
            <p class="text-muted">Selected Predefined User ID: {{ selectedPredefined }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import ChoicesSelect from '@/components/ChoicesSelect.vue';
import { searchConfigs, type ApiSearchConfig } from '@/composables/useApiSearch';

// Reactive values for selected items
const selectedUser = ref(null);
const selectedUserGroup = ref(null);
const selectedTags = ref([]);
const selectedProcedure = ref(null);
const selectedCustom = ref(null);
const selectedLegacy = ref(null);
const selectedPredefined = ref(null);

// Search configurations for different entities
const userSearchConfig: ApiSearchConfig = {
    ...searchConfigs.users,
    additionalParams: {
        isActive: true // Only search active users
    }
};

const userGroupSearchConfig: ApiSearchConfig = {
    endpoint: '/api/user-groups/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        with: ['admin', 'users'] // Include relationships
    }
};

const tagSearchConfig: ApiSearchConfig = {
    endpoint: '/api/tags/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        system: false // Exclude system tags
    }
};

const procedureSearchConfig: ApiSearchConfig = {
    endpoint: '/api/procedures/filter',
    searchParam: 'searchKey',
    valueField: 'id',
    labelField: 'title',
    dataPath: 'data.data',
    additionalParams: {
        activePeriod: '1' // Only active procedures
    }
};

// Example of a completely different API structure
const customSearchConfig: ApiSearchConfig = {
    endpoint: '/api/external/search',
    searchParam: 'query',
    valueField: 'uuid',
    labelField: 'display_name',
    dataPath: 'results.items',
    minSearchLength: 3,
    resultsPerPage: 25,
    additionalParams: {
        type: 'active',
        include: 'metadata',
        sort: 'name'
    }
};

// Predefined options example
const predefinedUsers = ref([
    { value: 1, text: 'Admin User' },
    { value: 2, text: 'Test User' },
    { value: 3, text: 'Demo User' }
]);
</script>

<style scoped>
.choices-select-examples {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

h3 {
    color: #333;
    margin-bottom: 2rem;
}

h4 {
    color: #666;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.text-muted {
    color: #6c757d;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}
</style>
