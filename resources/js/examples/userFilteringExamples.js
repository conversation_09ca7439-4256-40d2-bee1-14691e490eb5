/**
 * Examples of how to use the new user filtering endpoints
 */

// Example 1: Using the dedicated JSON endpoint
async function fetchUsersWithFilters() {
    try {
        const response = await fetch('/api/users/filter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                searchKey: 'john',
                isActive: true,
                status: 'active',
                roles: ['admin', 'user'],
                rpp: 10,
                all: false,
                orderBy: ['name', 'email']
            })
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('Users:', data.data);
            return data.data;
        } else {
            console.error('Error fetching users');
        }
    } catch (error) {
        console.error('Network error:', error);
    }
}

// Example 2: Using the flexible endpoint with AJAX (returns JSON)
async function fetchUsersFlexibleAjax() {
    try {
        const response = await fetch('/users/flexible', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json', // This tells Laravel we want JSON
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                searchKey: 'jane',
                isActive: true,
                rpp: 15
            })
        });

        const data = await response.json();
        console.log('Flexible endpoint (JSON):', data);
        return data;
    } catch (error) {
        console.error('Error:', error);
    }
}

// Example 3: Using Axios (if you have it installed)
async function fetchUsersWithAxios() {
    try {
        const response = await axios.post('/api/users/filter', {
            searchKey: 'smith',
            isActive: true,
            rpp: 20
        });

        console.log('Users with Axios:', response.data);
        return response.data;
    } catch (error) {
        console.error('Axios error:', error);
    }
}

// Example 4: Using with Inertia.js router for AJAX-like behavior
import { router } from '@inertiajs/vue3';

function searchUsersWithInertia(searchTerm) {
    router.post('/api/users/filter', 
        { 
            searchKey: searchTerm,
            rpp: 10 
        },
        {
            preserveState: true,
            onSuccess: (page) => {
                console.log('Search results:', page.props);
            },
            onError: (errors) => {
                console.error('Search errors:', errors);
            }
        }
    );
}

// Example 5: Vue 3 Composition API example
import { ref, reactive } from 'vue';

export function useUserFiltering() {
    const users = ref([]);
    const loading = ref(false);
    const error = ref(null);

    const filters = reactive({
        searchKey: '',
        isActive: null,
        status: '',
        roles: [],
        rpp: 20,
        all: false
    });

    const fetchUsers = async () => {
        loading.value = true;
        error.value = null;

        try {
            const response = await fetch('/api/users/filter', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(filters)
            });

            const data = await response.json();
            
            if (data.success) {
                users.value = data.data;
            } else {
                error.value = 'Failed to fetch users';
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    };

    const updateFilter = (key, value) => {
        filters[key] = value;
        fetchUsers();
    };

    return {
        users,
        loading,
        error,
        filters,
        fetchUsers,
        updateFilter
    };
}

// Example 6: jQuery example (if you're using jQuery)
function fetchUsersWithJQuery() {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $.post('/api/users/filter', {
        searchKey: 'test',
        isActive: true,
        rpp: 10
    })
    .done(function(data) {
        console.log('jQuery success:', data);
    })
    .fail(function(xhr, status, error) {
        console.error('jQuery error:', error);
    });
}

export {
    fetchUsersWithFilters,
    fetchUsersFlexibleAjax,
    fetchUsersWithAxios,
    searchUsersWithInertia,
    fetchUsersWithJQuery
};
