<template>
    <div class="test-choices-select p-4">
        <h3>Test ChoicesSelect Component</h3>
        
        <!-- Debug Info -->
        <div class="debug-info mb-4 p-3 bg-light border rounded">
            <h5>Debug Information:</h5>
            <p><strong>CSRF Token:</strong> {{ csrfToken || 'NOT FOUND' }}</p>
            <p><strong>API Endpoint:</strong> /api/users/filter</p>
            <p><strong>Last Error:</strong> {{ lastError || 'None' }}</p>
        </div>

        <!-- Test Button -->
        <div class="mb-4">
            <button @click="testApiCall" class="btn btn-primary me-2" :disabled="testing">
                {{ testing ? 'Testing...' : 'Test API Call' }}
            </button>
            <button @click="clearResults" class="btn btn-secondary">Clear Results</button>
        </div>

        <!-- API Test Results -->
        <div v-if="apiResults" class="api-results mb-4 p-3 bg-success bg-opacity-10 border border-success rounded">
            <h5 class="text-success">API Test Results:</h5>
            <pre>{{ JSON.stringify(apiResults, null, 2) }}</pre>
        </div>

        <!-- ChoicesSelect Component Test -->
        <div class="choices-test">
            <h5>ChoicesSelect Component Test:</h5>
            <ChoicesSelect
                id="test-users"
                v-model="selectedUsers"
                label="Search Users"
                :search-config="{
                    endpoint: '/api/users/filter',
                    searchParam: 'searchKey',
                    valueField: 'id',
                    labelField: 'name',
                    dataPath: 'data.data',
                    additionalParams: { isActive: true, rpp: 10 }
                }"
                :multiple="true"
                placeholder="Type to search users..."
            />
            <p class="mt-2"><strong>Selected Users:</strong> {{ selectedUsers }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import ChoicesSelect from '@/components/ChoicesSelect.vue';

// Reactive state
const csrfToken = ref<string | null>(null);
const lastError = ref<string | null>(null);
const testing = ref(false);
const apiResults = ref<any>(null);
const selectedUsers = ref<number[]>([]);

// Get CSRF token on mount
onMounted(() => {
    csrfToken.value = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || null;
});

// Test API call directly
const testApiCall = async () => {
    testing.value = true;
    lastError.value = null;
    apiResults.value = null;

    try {
        const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        
        if (!token) {
            throw new Error('CSRF token not found');
        }

        console.log('Making API call with token:', token.substring(0, 10) + '...');

        const response = await fetch('/api/users/filter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': token,
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                searchKey: 'test',
                rpp: 5,
                isActive: true
            })
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Error response:', errorText);
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }

        const data = await response.json();
        console.log('Success response:', data);
        apiResults.value = data;

    } catch (error) {
        console.error('API test error:', error);
        lastError.value = error instanceof Error ? error.message : 'Unknown error';
    } finally {
        testing.value = false;
    }
};

const clearResults = () => {
    apiResults.value = null;
    lastError.value = null;
    selectedUsers.value = [];
};
</script>

<style scoped>
.test-choices-select {
    max-width: 800px;
    margin: 0 auto;
}

.debug-info {
    font-family: monospace;
    font-size: 0.9rem;
}

.api-results pre {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.8rem;
}

h3 {
    color: #333;
    margin-bottom: 1.5rem;
}

h5 {
    color: #666;
    margin-bottom: 0.5rem;
}
</style>
