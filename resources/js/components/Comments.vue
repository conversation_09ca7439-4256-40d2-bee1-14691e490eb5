<template>
    <b-card no-body class="chat-content rounded-0 mb-0 shadow-none">
        <!--        <b-card-header class="border-bottom px-3 py-2">
            <div class="d-flex align-items-center justify-content-between py-1">
                <div class="d-flex align-items-center gap-2">
                    <img :src="'https://i.pravatar.cc/150?u=5'" class="avatar-lg rounded-circle" alt="" />

                    <div>
                        <h5 class="lh-base my-0">
                            <a href="#" class="text-reset"><PERSON></a>
                        </h5>
                        <p class="text-muted mb-0"><small class="ti ti-circle-filled text-success"></small> Active</p>
                    </div>
                </div>

                <div class="d-flex align-items-center gap-2">
                    &lt;!&ndash;                    <a href="#" class="btn btn-sm btn-icon btn-ghost-light d-none d-xl-flex" v-b-tooltip.top="'Voice Call'">
                        <i class="ti ti-phone-call fs-20"></i>
                    </a>
                    <a href="#" class="btn btn-sm btn-icon btn-ghost-light d-none d-xl-flex" v-b-tooltip.top="'Voice Call'">
                        <i class="ti ti-video fs-20"></i>
                    </a>&ndash;&gt;

                    <a href="#" class="btn btn-sm btn-icon btn-ghost-light d-xl-flex">
                        <i class="ti ti-info-circle fs-20"></i>
                    </a>
                </div>
            </div>
        </b-card-header>-->

        <div>
            <simplebar class="chat-scroll p-3">
                <ul class="chat-list" data-apps-chat="messages-list">
                    <template v-for="(comment, idx) in comments" :key="`comment_${comment.id}`">
                        <li class="chat-group">  <!--:class="{ odd: item.recipient }"-->
                            <div class="chat-body">
                                <div>
                                    <h6 class="d-inline-flex">{{ comment.user.name }} {{ comment.user.surname }}</h6>
                                    <br>
                                    <h6  class="d-inline-flex  text-muted">Created at: {{ formatDateTime(comment.created_at) }}</h6> &nbsp;
                                    <h6 class="d-inline-flex text-muted">Updated at: {{ formatDateTime(comment.updated_at) }}</h6>
                                </div>
                                <div class="chat-message">
                                    <p>{{ comment.comment }}</p>
                                </div>

<!--                                <div v-for="message in comment.children_recursive" class="chat-message" :key="`message_${message.commentId}`">
                                    <p>{{ message }}</p>

&lt;!&ndash;                                    <b-dropdown class="chat-actions" :variant="null" size="sm" no-caret toggle-class="btn-link p-0">
                                        <template v-slot:button-content>
                                            <i class="ti ti-dots-vertical"></i>
                                        </template>

                                        <b-dropdown-item><i class="ti ti-copy fs-14 me-1 align-text-top"></i> Copy Message </b-dropdown-item>
                                        <b-dropdown-item><i class="ti ti-edit-circle fs-14 me-1 align-text-top"></i>Edit </b-dropdown-item>
                                        <b-dropdown-item><i class="ti ti-trash fs-14 me-1 align-text-top"></i>Delete </b-dropdown-item>
                                    </b-dropdown>&ndash;&gt;
                                </div>-->
                            </div>
                        </li>
                    </template>
                </ul>
            </simplebar>

            <div class="border-top position-sticky bottom-0 mb-0 w-100 p-3">
                <b-form class="row align-items-start g-2" name="chat-form" id="chat-form">
                    <!--                    <div class="col-auto">
                        <b-button :variant="null" class="btn-icon btn-soft-warning">
                            <Icon icon="solar:smile-circle-outline" class="fs-20" />
                        </b-button>
                    </div>-->

                    <b-col>
                        <b-form-textarea id="textarea" placeholder="Type Message..." rows="3" max-rows="6"></b-form-textarea>
                    </b-col>

                    <div class="col-sm-auto">
                        <div class="d-flex align-items-center gap-1">
                            <b-button variant="success" class="btn-icon"><i class="ti ti-send"></i></b-button>
                            <!--                            <a href="#" class="btn btn-icon btn-soft-primary"><i class="ti ti-microphone"></i> </a>-->
                            <a href="#" class="btn btn-icon btn-soft-primary"><i class="ti ti-paperclip"></i></a>
                        </div>
                    </div>
                </b-form>
            </div>
        </div>
    </b-card>
</template>

<script setup lang="ts">
import simplebar from 'simplebar-vue';
import { Comment } from '@/types/comments';
import { formatDateTime } from '@/helpers/other';


defineProps<{
    comments: Comment[];
}>();
</script>
