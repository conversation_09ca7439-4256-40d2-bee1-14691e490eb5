<template>
    <div class="user-filter-example">
        <h3>User Filtering Example</h3>
        
        <!-- Filter Form -->
        <div class="filter-form mb-4">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="searchKey" class="form-label">Search</label>
                    <input 
                        id="searchKey"
                        v-model="filters.searchKey" 
                        type="text" 
                        class="form-control" 
                        placeholder="Search users..."
                        @input="debouncedSearch"
                    />
                </div>
                
                <div class="col-md-3">
                    <label for="status" class="form-label">Status</label>
                    <select id="status" v-model="filters.status" class="form-select" @change="fetchUsers">
                        <option value="">All</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="rpp" class="form-label">Per Page</label>
                    <select id="rpp" v-model="filters.rpp" class="form-select" @change="fetchUsers">
                        <option value="10">10</option>
                        <option value="20">20</option>
                        <option value="50">50</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-flex gap-2">
                        <button @click="fetchUsers" class="btn btn-primary" :disabled="loading">
                            <i class="ti ti-search me-1"></i>
                            {{ loading ? 'Searching...' : 'Search' }}
                        </button>
                        <button @click="clearFilters" class="btn btn-outline-secondary">
                            <i class="ti ti-x me-1"></i>
                            Clear
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="text-center py-4">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <!-- Error State -->
        <div v-if="error" class="alert alert-danger">
            <i class="ti ti-alert-circle me-2"></i>
            {{ error }}
        </div>

        <!-- Results -->
        <div v-if="!loading && users.length > 0" class="results">
            <h4>Results ({{ users.length }} users)</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="user in users" :key="user.id">
                            <td>{{ user.id }}</td>
                            <td>{{ user.name }} {{ user.surname }}</td>
                            <td>{{ user.email }}</td>
                            <td>
                                <span :class="user.isActive ? 'badge bg-success' : 'badge bg-secondary'">
                                    {{ user.isActive ? 'Active' : 'Inactive' }}
                                </span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- No Results -->
        <div v-if="!loading && users.length === 0 && !error" class="text-center py-4">
            <i class="ti ti-users-off fs-1 text-muted"></i>
            <p class="text-muted mt-2">No users found matching your criteria</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { debounce } from 'lodash-es';

// Types
interface User {
    id: number;
    name: string;
    surname: string;
    email: string;
    isActive: boolean;
}

interface Filters {
    searchKey: string;
    status: string;
    rpp: number;
    isActive?: boolean;
}

// Reactive state
const users = ref<User[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

const filters = reactive<Filters>({
    searchKey: '',
    status: '',
    rpp: 20
});

// Methods
const fetchUsers = async () => {
    loading.value = true;
    error.value = null;

    try {
        // Prepare filters
        const requestData: any = { ...filters };
        
        // Convert status to isActive boolean if needed
        if (filters.status === 'active') {
            requestData.isActive = true;
        } else if (filters.status === 'inactive') {
            requestData.isActive = false;
        }

        const response = await fetch('/api/users/filter', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
            },
            body: JSON.stringify(requestData)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.success) {
            // Handle both paginated and non-paginated responses
            users.value = data.data.data || data.data;
        } else {
            error.value = data.message || 'Failed to fetch users';
        }
    } catch (err) {
        error.value = err instanceof Error ? err.message : 'An error occurred';
        console.error('Error fetching users:', err);
    } finally {
        loading.value = false;
    }
};

const clearFilters = () => {
    filters.searchKey = '';
    filters.status = '';
    filters.rpp = 20;
    fetchUsers();
};

// Debounced search for real-time filtering
const debouncedSearch = debounce(() => {
    fetchUsers();
}, 500);

// Load initial data
onMounted(() => {
    fetchUsers();
});
</script>

<style scoped>
.user-filter-example {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.filter-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.results {
    margin-top: 20px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

.badge {
    font-size: 0.75em;
}
</style>
