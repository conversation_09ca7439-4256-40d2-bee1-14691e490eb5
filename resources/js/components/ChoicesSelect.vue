<template>
    <label v-if="label" :for="id" class="form-label" :class="labelClass">{{ label }}</label>
    <select :id="id" :value="modelValue" v-bind="$attrs" class="form-select" :multiple="multiple">
        <slot />
        <template v-if="options">
            <option v-for="(option, idx) in options" :key="idx" :value="option.value">
                {{ option.text }}
            </option>
        </template>
    </select>
</template>

<script setup lang="ts">
import Choices from 'choices.js';
import { nextTick, onMounted, watch } from 'vue';
import { router } from '@inertiajs/vue3';
import { useApiSearch, type ApiSearchConfig } from '@/composables/useApiSearch';

// Re-export the interface for convenience
type SearchConfig = ApiSearchConfig;

const props = defineProps<{
    id: string;
    modelValue?: any;
    options?: Array<{ value: any; text: string }>;
    multiple?: boolean;
    choiceOptions?: object;
    searchRoute?: string; // Deprecated: use searchConfig instead
    searchConfig?: SearchConfig;
    label?: string;
    labelClass?: string;
}>();

const emit = defineEmits(['update:modelValue']);

let choices: Choices;

// Use the search composable
const { search } = useApiSearch();

// Abstract search function
const performSearch = async (searchTerm: string) => {
    try {
        let config: ApiSearchConfig;

        if (props.searchConfig) {
            // Use new searchConfig approach
            config = props.searchConfig;
        } else if (props.searchRoute) {
            // Legacy support for searchRoute - default to users
            config = {
                endpoint: '/api/users/filter',
                searchParam: 'searchKey',
                valueField: 'id',
                labelField: 'name',
                dataPath: 'data.data'
            };
        } else {
            return;
        }

        const results = await search(searchTerm, config);

        const newOptions = results.map(result => ({
            value: result.value,
            label: result.label
        }));

        choices.setChoices(newOptions, 'value', 'label', true);
    } catch (error) {
        console.error('Error performing search:', error);
    }
};

onMounted(async () => {
    await nextTick();

    const selectElement = document.querySelector(`#${props.id}`) as HTMLSelectElement;
    choices = new Choices(selectElement, {
        ...props.choiceOptions,
        searchEnabled: !!props.searchRoute,
        searchChoices: false,
        searchFloor: 2,
    });

    // Handle search functionality (both legacy searchRoute and new searchConfig)
    if (props.searchRoute || props.searchConfig) {
        selectElement.addEventListener('search', (event: Event) => {
            const customEvent = event as CustomEvent<{ value: string }>;
            const searchTerm = customEvent.detail.value;
            if (searchTerm.length >= 2) {
                performSearch(searchTerm);
            }
        });
    }

    // Set initial values
    setTimeout(() => {
        if (props.modelValue !== undefined && props.modelValue !== null) {
            if (props.multiple && Array.isArray(props.modelValue)) {
                choices.setChoiceByValue(props.modelValue.map(String));
            } else if (!props.multiple) {
                choices.setChoiceByValue(String(props.modelValue));
            }
        }
    }, 100);
});
</script>
