<template>
    <label v-if="label" :for="id" class="form-label" :class="labelClass">{{ label }}</label>
    <select :id="id" :value="modelValue" v-bind="$attrs" class="form-select" :multiple="multiple">
        <slot />
        <template v-if="options">
            <option v-for="(option, idx) in options" :key="idx" :value="option.value">
                {{ option.text }}
            </option>
        </template>
    </select>
</template>

<script setup lang="ts">
import Choices from 'choices.js';
import { nextTick, onMounted, watch } from 'vue';
import { router } from '@inertiajs/vue3';

const props = defineProps<{
    id: string;
    modelValue?: any;
    options?: Array<{ value: any; text: string }>;
    multiple?: boolean;
    choiceOptions?: object;
    searchRoute?: string;
    label?: string;
    labelClass?: string;
}>();

const emit = defineEmits(['update:modelValue']);

let choices: Choices;

onMounted(async () => {
    await nextTick();

    const selectElement = document.querySelector(`#${props.id}`) as HTMLSelectElement;
    choices = new Choices(selectElement, {
        ...props.choiceOptions,
        searchEnabled: !!props.searchRoute,
        searchChoices: false,
        searchFloor: 2,
    });

    if (props.searchRoute) {
        selectElement.addEventListener('search', (event: Event) => {
            const customEvent = event as CustomEvent<{ value: string }>;
            const searchTerm = customEvent.detail.value;
            if (searchTerm.length >= 2) {
                router.post(route(props.searchRoute!),
                    { searchKey: searchTerm },
                    {
                        preserveState: true,
                        onSuccess: (page: any) => {
                            const users = page.props.users?.data || [];
                            const newOptions = users.map((user: any) => ({
                                value: user.id,
                                label: user.name
                            }));
                            choices.setChoices(newOptions, 'value', 'label', true);
                        }
                    }
                );
            }
        });
    }

    // Set initial values
    setTimeout(() => {
        if (props.modelValue !== undefined && props.modelValue !== null) {
            if (props.multiple && Array.isArray(props.modelValue)) {
                choices.setChoiceByValue(props.modelValue.map(String));
            } else if (!props.multiple) {
                choices.setChoiceByValue(String(props.modelValue));
            }
        }
    }, 100);
});
</script>
