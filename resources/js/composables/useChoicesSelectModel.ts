/**
 * Composable for handling ChoicesSelect v-model with object arrays
 * Converts between object arrays and ID arrays for ChoicesSelect compatibility
 */

import { ref, computed, watch, type Ref } from 'vue';

export interface SelectableItem {
    id: number;
    [key: string]: any;
}

export interface ChoicesSelectModelOptions {
    valueField?: string;
    labelField?: string;
    autoSave?: boolean;
    onSave?: (ids: number[]) => Promise<void> | void;
}

export function useChoicesSelectModel<T extends SelectableItem>(
    items: Ref<T[]>,
    options: ChoicesSelectModelOptions = {}
) {
    const {
        valueField = 'id',
        labelField = 'name',
        autoSave = false,
        onSave
    } = options;

    // Reactive array of selected IDs
    const selectedIds = ref<number[]>([]);

    // Initialize selected IDs from items
    const initializeFromItems = () => {
        selectedIds.value = items.value.map(item => item[valueField] as number);
    };

    // Initialize on first load
    initializeFromItems();

    // Watch for changes in items and update selectedIds
    watch(items, () => {
        initializeFromItems();
    }, { deep: true });

    // Computed property for ChoicesSelect options
    const options = computed(() => {
        return items.value.map(item => ({
            value: item[valueField],
            text: item[labelField] || item[valueField]
        }));
    });

    // Computed property for v-model compatibility
    const modelValue = computed({
        get: () => selectedIds.value,
        set: async (newIds: number[]) => {
            selectedIds.value = newIds;
            
            if (autoSave && onSave) {
                try {
                    await onSave(newIds);
                } catch (error) {
                    console.error('Error auto-saving selection:', error);
                }
            }
        }
    });

    // Method to manually save changes
    const save = async () => {
        if (onSave) {
            try {
                await onSave(selectedIds.value);
                return true;
            } catch (error) {
                console.error('Error saving selection:', error);
                return false;
            }
        }
        return true;
    };

    // Method to get selected items (full objects)
    const getSelectedItems = computed(() => {
        return items.value.filter(item => 
            selectedIds.value.includes(item[valueField] as number)
        );
    });

    // Method to add item by ID
    const addItem = (id: number) => {
        if (!selectedIds.value.includes(id)) {
            selectedIds.value.push(id);
        }
    };

    // Method to remove item by ID
    const removeItem = (id: number) => {
        const index = selectedIds.value.indexOf(id);
        if (index > -1) {
            selectedIds.value.splice(index, 1);
        }
    };

    // Method to clear all selections
    const clear = () => {
        selectedIds.value = [];
    };

    // Method to select all items
    const selectAll = () => {
        selectedIds.value = items.value.map(item => item[valueField] as number);
    };

    return {
        selectedIds: selectedIds as Ref<number[]>,
        options,
        modelValue,
        getSelectedItems,
        addItem,
        removeItem,
        clear,
        selectAll,
        save,
        initializeFromItems
    };
}

// Specialized version for users
export function useUserSelectModel(users: Ref<any[]>, options: Omit<ChoicesSelectModelOptions, 'valueField' | 'labelField'> = {}) {
    return useChoicesSelectModel(users, {
        valueField: 'id',
        labelField: 'name',
        ...options
    });
}

// Specialized version for user groups
export function useUserGroupSelectModel(userGroups: Ref<any[]>, options: Omit<ChoicesSelectModelOptions, 'valueField' | 'labelField'> = {}) {
    return useChoicesSelectModel(userGroups, {
        valueField: 'id',
        labelField: 'title',
        ...options
    });
}

// Specialized version for tags
export function useTagSelectModel(tags: Ref<any[]>, options: Omit<ChoicesSelectModelOptions, 'valueField' | 'labelField'> = {}) {
    return useChoicesSelectModel(tags, {
        valueField: 'id',
        labelField: 'title',
        ...options
    });
}

// Example usage:
/*
// In your component:
import { useUserSelectModel } from '@/composables/useChoicesSelectModel';

const procedureStepUsers = ref([
    { id: 1, name: 'John Doe', email: '<EMAIL>' },
    { id: 2, name: 'Jane Smith', email: '<EMAIL>' }
]);

const userSelect = useUserSelectModel(procedureStepUsers, {
    autoSave: true,
    onSave: async (userIds) => {
        // Save to server
        await saveStepUsers(stepId, userIds);
    }
});

// In template:
<ChoicesSelect
    v-model="userSelect.modelValue.value"
    :options="userSelect.options.value"
    :search-config="{ ... }"
    :multiple="true"
/>
*/
