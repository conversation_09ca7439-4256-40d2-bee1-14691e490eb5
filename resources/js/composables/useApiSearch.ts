/**
 * Composable for making abstract API search requests
 * Supports different endpoints, data structures, and field mappings
 */

export interface ApiSearchConfig {
    endpoint: string;
    searchParam?: string;
    valueField?: string;
    labelField?: string;
    dataPath?: string;
    additionalParams?: Record<string, any>;
    minSearchLength?: number;
    resultsPerPage?: number;
}

export interface SearchResult {
    value: any;
    label: string;
    original?: any;
}

export function useApiSearch() {
    /**
     * Perform a search request with the given configuration
     */
    const search = async (
        searchTerm: string, 
        config: ApiSearchConfig
    ): Promise<SearchResult[]> => {
        const {
            endpoint,
            searchParam = 'searchKey',
            valueField = 'id',
            labelField = 'name',
            dataPath = 'data.data',
            additionalParams = {},
            minSearchLength = 2,
            resultsPerPage = 50
        } = config;

        // Validate search term length
        if (searchTerm.length < minSearchLength) {
            return [];
        }

        try {
            const requestBody = {
                [searchParam]: searchTerm,
                rpp: resultsPerPage,
                all: false,
                ...additionalParams
            };

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.success) {
                // Navigate to the data using the specified path
                const items = getNestedValue(data, dataPath) || data.data || data;
                
                return items.map((item: any) => ({
                    value: getNestedValue(item, valueField),
                    label: getNestedValue(item, labelField),
                    original: item
                }));
            } else {
                console.error('Search failed:', data.message || 'Unknown error');
                return [];
            }
        } catch (error) {
            console.error('Error performing search:', error);
            return [];
        }
    };

    /**
     * Helper function to get nested object values using dot notation
     */
    const getNestedValue = (obj: any, path: string): any => {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    /**
     * Create a search function with predefined configuration
     */
    const createSearchFunction = (config: ApiSearchConfig) => {
        return (searchTerm: string) => search(searchTerm, config);
    };

    return {
        search,
        createSearchFunction,
        getNestedValue
    };
}

// Predefined configurations for common use cases
export const searchConfigs = {
    users: {
        endpoint: '/api/users/filter',
        searchParam: 'searchKey',
        valueField: 'id',
        labelField: 'name',
        dataPath: 'data.data'
    } as ApiSearchConfig,

    userGroups: {
        endpoint: '/api/user-groups/filter',
        searchParam: 'searchKey',
        valueField: 'id',
        labelField: 'title',
        dataPath: 'data.data'
    } as ApiSearchConfig,

    tags: {
        endpoint: '/api/tags/filter',
        searchParam: 'searchKey',
        valueField: 'id',
        labelField: 'title',
        dataPath: 'data.data'
    } as ApiSearchConfig,

    procedures: {
        endpoint: '/api/procedures/filter',
        searchParam: 'searchKey',
        valueField: 'id',
        labelField: 'title',
        dataPath: 'data.data'
    } as ApiSearchConfig,

    // Example for a different API structure
    customApi: {
        endpoint: '/api/custom/search',
        searchParam: 'query',
        valueField: 'uuid',
        labelField: 'display_name',
        dataPath: 'results.items',
        additionalParams: {
            type: 'active',
            include: 'metadata'
        }
    } as ApiSearchConfig
};

// Type-safe search functions for common entities
export const useUserSearch = () => {
    const { search } = useApiSearch();
    return (searchTerm: string) => search(searchTerm, searchConfigs.users);
};

export const useUserGroupSearch = () => {
    const { search } = useApiSearch();
    return (searchTerm: string) => search(searchTerm, searchConfigs.userGroups);
};

export const useTagSearch = () => {
    const { search } = useApiSearch();
    return (searchTerm: string) => search(searchTerm, searchConfigs.tags);
};

export const useProcedureSearch = () => {
    const { search } = useApiSearch();
    return (searchTerm: string) => search(searchTerm, searchConfigs.procedures);
};
