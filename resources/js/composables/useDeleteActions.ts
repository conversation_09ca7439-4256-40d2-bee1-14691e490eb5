import {router} from '@inertiajs/vue3';
import {computed, ref} from 'vue';
import {useI18n} from 'vue-i18n';
import Swal from 'sweetalert2';

interface DeleteConfig {
    entityName: string; // e.g., 'tag', 'userGroup'
    deleteRoute: string; // e.g., 'deleteTag', 'deleteUserGroup'
    massDeleteRoute: string; // e.g., 'massDeleteTags', 'massDeleteUserGroups'
    successMessage: string; // e.g., 'alerts.tags.delete.success'
    errorMessage: string; // e.g., 'alerts.tags.delete.error'
    bulkSuccessMessage: string; // e.g., 'alerts.tags.delete.successBulk'
    bulkErrorMessage: string; // e.g., 'alerts.tags.delete.errorBulk'
    bulkDeleteTranslationKey: string; // e.g., 'tags.bulkDelete', 'userGroups.bulkDelete'
}

interface Item {
    id: number;
    title?: string;
    name?: string;
    [key: string]: any;
}

export function useDeleteActions<T extends Item>(
    items: () => T[],
    config: DeleteConfig
) {
    const { t } = useI18n();

    // Selection state
    const selectedRows = ref<number[]>([]);

    // Computed properties
    const selectedItems = computed(() =>
        items().filter((item) => selectedRows.value.includes(item.id))
    );

    const allSelected = computed(() =>
        selectedRows.value.length === items().length && items().length > 0
    );

    // Selection methods
    const toggleSelectAll = () => {
        if (allSelected.value) {
            selectedRows.value = [];
        } else {
            selectedRows.value = items().map((item) => item.id);
        }
    };

    // Single delete with confirmation
    const deleteWithConfirm = (id: number) => {
        Swal.fire({
            title: t('alerts.common.delete.title'),
            text: t('alerts.common.delete.text'),
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: t('alerts.common.delete.confirm'),
            cancelButtonText: t('alerts.common.delete.cancel'),
        }).then((result) => {
            if (result.isConfirmed) {
                router.delete(route(config.deleteRoute, id), {
                    onSuccess: () => {
                        Swal.fire(
                            t('alerts.common.delete.successTitle'),
                            t(config.successMessage),
                            'success'
                        );
                    },
                    onError: (errors) => {
                        Swal.fire(
                            t('alerts.common.delete.errorTitle'),
                            errors.message || t(config.errorMessage),
                            'error'
                        );
                    },
                });
            }
        });
    };

    // Bulk delete with confirmation
    const deleteMultipleWithConfirm = () => {
        const itemTitles = selectedItems.value.map((item) => item.title).join('<br />');
        const bulkDeleteParams = { [config.entityName + 's']: itemTitles };

        Swal.fire({
            title: t('common.bulkDelete'),
            html: `${t(config.bulkDeleteTranslationKey, bulkDeleteParams)} ${t('alerts.common.delete.text')}`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: t('alerts.common.delete.confirm'),
            cancelButtonText: t('alerts.common.delete.cancel'),
        }).then((result) => {
            if (result.isConfirmed) {
                router.delete(route(config.massDeleteRoute, { ids: selectedRows.value }), {
                    onSuccess: () => {
                        Swal.fire(
                            t('alerts.common.delete.successTitle'),
                            t(config.bulkSuccessMessage),
                            'success'
                        );
                    },
                    onError: (errors) => {
                        Swal.fire(
                            t('alerts.common.delete.errorTitle'),
                            errors.message || t(config.bulkErrorMessage),
                            'error'
                        );
                    },
                    onFinish: () => {
                        selectedRows.value = [];
                    },
                });
            }
        });
    };

    return {
        // State
        selectedRows,

        // Computed
        selectedItems,
        allSelected,

        // Methods
        toggleSelectAll,
        deleteWithConfirm,
        deleteMultipleWithConfirm,
    };
}
