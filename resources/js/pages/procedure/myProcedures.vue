<template>
    <VerticalLayout>
        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="d-flex align-items-center flex-wrap gap-2">
                        <h4 class="header-title me-auto">PROCEDURES ??? <span class="text-muted fw-normal fs-14">(SOME TEXT)</span>
                        </h4>

                        <div class="search-bar">
                            <b-form-input size="sm" class="search" placeholder="Search Here..." />
                        </div>

                        <div class="w-auto">
                            select box
<!--                            <b-form-select size="sm" v-model="selectedOption" :options="transactionOptions" />-->
                        </div>

<!--                        <a href="javascript:void(0);" class="btn btn-sm btn-soft-primary">Export <i class="ti ti-file-export ms-1"></i></a>-->
                    </b-card-header>

                    <b-card-body class="p-0">
                        <div class="table-card">
                            <b-table-simple responsive hover class="table-custom mb-0">
                                <b-thead class="bg-light thead-sm bg-opacity-50">
                                    <b-tr class="text-uppercase fs-12">
                                        <b-th scope="col" class="text-muted">ID</b-th>
                                        <b-th scope="col" class="text-muted">Title</b-th>
                                        <b-th scope="col" class="text-muted">Steps</b-th>
                                    </b-tr>
                                </b-thead>

                                <b-tbody>
                                    <b-tr  v-for="procedure in procedures.data" :key="`procedure_${procedure.id}`">
                                        <b-td class="align-top">#{{ procedure.id }}</b-td>
                                        <b-td class="align-top">
                                            <Link :href="procedure.link" class="link-reset underline text-secondary">{{ procedure.title }}</Link>
                                        </b-td>
                                        <b-td v-if="procedure.steps.length"  class="align-top p-0">
                                            <b-table-simple responsive hover class="mb-0" style="border-left: 2px solid rgb(238, 242, 247);">
                                                <b-tbody>
                                                    <b-tr v-for="(procedureStep, index) in procedure.steps" :key="`procedureStep_${procedure.id}`">
                                                        <b-td class="align-top" style="width: 40px;">
                                                            #{{ index + 1 }}
                                                        </b-td>
                                                        <b-td class="align-top">
                                                            <h5 class="fs-14 my-1 mt-0">{{ procedureStep.description }}</h5>
                                                            <span class="text-muted fs-12">{{ formatDate(procedureStep.starts_at) }} - {{ formatDate(procedureStep.ends_at) }}</span>
                                                        </b-td>
                                                        <b-td class="align-top" style="width: 120px;">
                                                            <span v-if="!procedureStep.isCurrent" class="badge px-2 py-1 badge-soft-danger">Expired</span>
                                                            <span v-else class="badge px-2 py-1 badge-soft-success"> Ongoing </span>
                                                        </b-td>
                                                    </b-tr>
                                                </b-tbody>
                                            </b-table-simple>
                                        </b-td>
                                        <b-td v-else class="align-middle text-center">NO STEPS</b-td>
                                    </b-tr>
                                </b-tbody>
                            </b-table-simple>
                        </div>
                    </b-card-body>

                    <b-card-footer class="border-top border-light">
                        PAGINATION????
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>


    </VerticalLayout>
</template>

<script setup lang="ts">
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {Procedure} from '@/types/procedure';
import { Link, usePage } from '@inertiajs/vue3';
import {ref} from 'vue';
import {useI18n} from 'vue-i18n';
import {formatDate} from '@/helpers/other';
import {
    BCard,
    BCardBody, BCardFooter,
    BCardHeader,
    BCol,
    BFormInput,
    BRow,
    BTableSimple, BTbody, BTd,
    BTh,
    BThead,
    BTr
} from 'bootstrap-vue-next';

const {t} = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    procedures: {
        data: Procedure[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');
const activePeriod = ref(usePage().props.activePeriod as string || '');

</script>
