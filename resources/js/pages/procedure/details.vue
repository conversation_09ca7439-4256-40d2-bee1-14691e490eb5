<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-bubble" :title="editMode ? t('procedures.edit') : t('procedures.add')" :subtitle="t('procedures.list')"
                   subtitleRoute="procedureIndex"/>

        <b-card no-body>
            <b-card-header class="header-title mb-0 border-bottom border-solid">
                <h4 class="header-title mb-0">TOTO: General Info</h4>
            </b-card-header>
            <b-card-body>
                <b-row>
                    <b-col xl="6" lg="12">
                        <b-form-group label-for="title" class="mb-3">
                            <template #label><i class="ti ti-article fs-16 me-1 align-text-top"/>{{ t('common.title') }}</template>
                            <b-form-input v-model="form.title" type="text" name="title" :placeholder="t('userGroups.editPlaceholder')"/>
                        </b-form-group>
                    </b-col>
                    <b-col xl="6" lg="12">

                    </b-col>
                </b-row>
                <b-form-group :label="t('common.description')" label-for="description" class="mb-3">
                    <template #label><i class="ti ti-file-description fs-16 me-1 align-text-top"/>{{ t('common.description') }}</template>
                    <Editor v-model="form.description"/>
                </b-form-group>
            </b-card-body>
        </b-card>
        <b-card no-body>
            <b-card-header class="header-title mb-0 border-bottom border-solid">
                <h4 class="header-title mb-0">TOTO: Steps</h4>
            </b-card-header>
            <b-card-body>
                <div v-if="form.steps.length"  class="align-top p-0">
                    <b-table-simple responsive hover class="mb-0">
                        <b-thead>
                            <b-tr>
                                <b-th class="text-center" style="width: 40px;">#</b-th>
                                <b-th>{{ t('procedures.description') }}</b-th>
                                <b-th>{{ t('procedures.timePeriod') }}</b-th>
                                <b-th>{{ t('procedures.userGroups') }}</b-th>
                                <b-th>{{ t('procedures.users') }}</b-th>
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="(procedureStep, index) in form.steps" :key="`procedureStep_${procedureStep.id}`">
                                <b-td class="align-top" style="width: 40px;">
                                    #{{ index + 1 }}
                                </b-td>
                                <b-td class="align-top">
                                    <h5 class="fs-14 my-1 mt-0">#{{ procedureStep.id }} {{ procedureStep.description }}</h5>
                                    <span class="d-block mb-1">{{ procedureStep.type }}</span>
                                    <span v-if="!procedureStep.isCurrent" class="badge px-2 py-1 badge-soft-danger">Expired</span>
                                    <span v-else class="badge px-2 py-1 badge-soft-success"> Ongoing </span>
                                </b-td>
                                <b-td class="align-top">
                                    <div class="text-muted fs-14 text-nowrap">{{ formatDate(procedureStep.starts_at) }} - {{ formatDate(procedureStep.ends_at) }}</div>
                                </b-td>
                                <b-td class="align-top">
                                    <h5 class="fs-14 my-1 mt-0">{{ procedureStep.user_groups.map((userGroup) => userGroup.title).join(', ') }}</h5>
                                </b-td>
                                <b-td class="align-top">
                                    <h5 class="fs-14 my-1 mt-0">{{ procedureStep.users.map((user) => user.name).join(', ') }}</h5>
                                    <b-form-group label-for="usersLookup" class="mb-3">
                                        <ChoicesSelect
                                            :id="`usersLookup_${procedureStep.id}`"
                                            :options="[]"
                                            :search-route="'userIndexPost'"
                                            :choice-options="{ addItems: false, removeItemButton: true, duplicateItemsAllowed: false, paste: false }"
                                            :multiple="true"
                                        />
                                    </b-form-group>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>
                </div>
            </b-card-body>
        </b-card>

        <b-card no-body>
            <b-card-body>
                <b-footer>
                    <div class="d-flex justify-content-between flex-wrap gap-2">
                        <Link :href="route('procedureIndex')" class="btn btn-secondary"><i
                            class="ti ti-arrow-left me-1"></i>{{ t('common.backToList') }}
                        </Link>
                        <b-button variant="primary" @click="handleSubmit" type="submit" :disabled="isSubmitting">
                            <b-spinner v-if="isSubmitting" small class="me-1" />
                            <i v-else class="ti ti-device-floppy me-1"></i>
                            {{ editMode ? t('common.update') : t('common.save') }}
                        </b-button>
                    </div>
                </b-footer>
            </b-card-body>
        </b-card>
    </VerticalLayout>
</template>

<script setup lang="ts">
import Editor from '@/components/Editor.vue';
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {useFormActions} from '@/composables/useFormActions';
import {Procedure, ProcedureFormData} from '@/types/procedure';
import {UserGroup} from '@/types/userGroup';
import {Link} from '@inertiajs/vue3';
import {
    BButton,
    BCard,
    BCardBody,
    BCardHeader,
    BCol,
    BFormGroup,
    BRow,
    BSpinner, BTableSimple,
    BTbody,
    BTd, BTh, BThead, BTr
} from 'bootstrap-vue-next';
import {computed, PropType, reactive} from 'vue';
import {useI18n} from 'vue-i18n';
import { formatDate } from '@/helpers/other';
import ChoicesSelect from '@/components/ChoicesSelect.vue';

const {t} = useI18n();

// Props
const props = defineProps({
    procedure: {
        type: Object as PropType<Procedure>,
        required: false,
    },
    userGroups: {
        type: Array as PropType<UserGroup[]>,
        required: false,
    },
});

// Initialize form data with existing userGroup data
const form = reactive<ProcedureFormData>({
    title: props.procedure?.title ?? '',
    description: props.procedure?.description ?? '',
    steps: props.procedure?.steps ?? [],
});

// Use the form actions composable
const {
    editMode,
    isSubmitting,
    handleSubmit
} = useFormActions(
    () => form,
    props.procedure,
    {
        entityName: 'procedure',
        addRoute: 'addProcedurePost',
        updateRoute: 'updateProcedurePost',
        addSuccessMessage: 'alerts.procedures.add.success',
        updateSuccessMessage: 'alerts.procedures.update.success',
        indexRoute: 'procedureIndex'
    },
    {
        prepareAddPayload: (formData) => ({
            title: formData.title,
            description: formData.description,
            steps: [
                {
                    starts_at: '2025-05-05 12:00:00',
                    ends_at: '2025-05-05 15:00:00',
                    description: 'test',
                    type: 'discussion',
                    userGroupIds: [1, 2],
                    userIds: []
                },
                {
                    starts_at: '2025-05-05 15:00:00',
                    ends_at: '2025-05-05 18:00:00',
                    description: 'test',
                    type: 'poll',
                    userGroupIds: [],
                    userIds: [1, 2]
                }
            ]
        }),
        prepareUpdatePayload: (formData, existingUserGroup) => ({
            id: existingUserGroup.id,
            title: formData.title,
            description: formData.description,
            steps: [
                {
                    starts_at: '2025-05-05 15:00:00',
                    ends_at: '2025-05-05 18:00:00',
                    description: 'test',
                    type: 'poll',
                    userGroupIds: [2],
                    userIds: [1, 3]
                }
            ]
        })
    }
);
</script>
