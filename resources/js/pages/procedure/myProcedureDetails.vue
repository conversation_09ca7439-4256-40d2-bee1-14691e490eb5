<template>
    <VerticalLayout>

        <PageTitle :subtitle="t('procedures.list')" :title="procedure.title" icon="ti ti-bubble"
                   subtitleRoute="myProcedures"/>

        <b-row class="justify-content-center">
            <b-col xxl="12">
                <div class="mb-4 text-center">
                    <h2 class="textdark fw-bold">{{ procedure.title }}</h2>
                </div>
                <div class="timeline" dir="ltr">
                    <div class="timeline-show mb-3 text-center">
                        <h5 class="time-show-name m-0 starts_at">{{ formatDate(procedure.steps[0].starts_at) }}</h5>
                    </div>

                    <div v-for="(procedureStep, idx) in procedure.steps" :key="`procedureStep_${procedure.id}`"
                         :class="idx%2 ? 'timeline-item-right' : ' timeline-item-left'"
                         class="timeline-lg-item">
                        <div class="timeline-desk">
                            <div class="timeline-box">
                                <span :class="idx%2 ? 'arrow' : 'arrow-alt'" class="arrow-alt shadow-none"></span>
                                <span class="timeline-icon avatar-lg">
                                    <span class="avatar-title bg-light rounded-circle">
                                        <i class="ti fs-28" :class="tempIconMap[procedureStep.type]"></i>
                                    </span>
                                </span>
                                <div class="d-flex justify-content-between gap-2">
                                    <h4 class="fw-bold text-dark mb-0">{{ formatStepNumber(idx + 1) }}</h4>
                                    <div class="text-end">
                                        <p class="fw-medium mb-0">{{ formatDate(procedureStep.starts_at) }} - {{ formatDate(procedureStep.ends_at) }}</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="d-flex justify-content-between">

                                        <h4 class="text-dark fw-semibold mb-2">
                                            {{ procedureStep.description }}
                                            <span v-if="!procedureStep.isCurrent" class="badge px-2 py-1 badge-soft-danger  fs-11 ms-1">Expired</span>
                                            <span v-else class="badge px-2 py-1 badge-soft-success  fs-11 ms-1"> Ongoing </span>
                                        </h4>

                                        <p class="mb-0 flex-shrink-0">{{ procedureStep.comments_count  }} comments</p>
                                    </div>
                                    <Link :href="procedureStep.link" class="btn btn-primary btn-sm"><i
                                        class="ti ti-eye me-1"></i>{{ t('common.read_more') }}
                                    </Link>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="timeline-show my-3 text-center">
                        <h5 class="time-show-name m-0 ends_at">{{ formatDate(procedure.steps[procedure.steps.length-1].ends_at) }}</h5>
                    </div>
                </div>
            </b-col>
        </b-row>


<!--        <b-row>
            <b-col v-for="(procedureStep, idx) in procedure.steps" :key="`procedureStep_${procedure.id}`" md="6" xl="6" xxl="3">
                <b-card no-body>
                    <b-card-body>
                        <div class="d-flex align-items-start gap-3">
                            <div>
                                <div class="avatar-lg bg-primary d-flex justify-content-center rounded bg-opacity-10">
                                    <i class="fs-32 text-primary align-self-center ti ti-bubble"></i>
                                </div>
                            </div>

                            <div>
                                <b-card-title class="d-flex align-items-center mb-1 gap-2" tag="h4">{{ procedureStep.description }}</b-card-title>
                                <p class="text-primary fw-medium fs-20 mb-0">
                                    {{ procedureStep.comments_count  }}
                                    <span class="fs-15 text-muted ms-1">Sxolia TODO</span>
                                </p>
                            </div>

&lt;!&ndash;                            <b-dropdown :variant="null" class="ms-auto" no-caret toggle-class="text-muted card-drop p-0">
                                <template v-slot:button-content>
                                    <i class="ti ti-dots-vertical"></i>
                                </template>
                                <b-dropdown-item>Action</b-dropdown-item>
                                <b-dropdown-item>Another Action</b-dropdown-item>
                                <b-dropdown-item>Something else here</b-dropdown-item>
                            </b-dropdown>&ndash;&gt;
                        </div>
                        <p class="text-dark fs-14 d-flex justify-content-between mb-1" >
                            STARTS AT TODO
                            <span class="text-dark fw-semibold">{{ formatDate(procedureStep.starts_at) }}</span>
                        </p>
                        <p class="text-dark fs-14 d-flex justify-content-between mb-0" >
                            ENDS AT TODO
                            <span class="text-dark fw-semibold">{{ formatDate(procedureStep.ends_at) }}</span>
                        </p>
                    </b-card-body>
                    <b-card-footer class="border-top border-dashed">
                        <a class="link-primary fw-medium" href="#">PERISSOTERA <i class="ti ti-arrow-right"></i></a>
                        <span v-if="!procedureStep.isCurrent" class="badge px-2 py-1 badge-soft-danger">Expired</span>
                        <span v-else class="badge px-2 py-1 badge-soft-success"> Ongoing </span>
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>-->
    </VerticalLayout>
</template>

<script lang="ts" setup>
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {Procedure} from '@/types/procedure';
import {useI18n} from 'vue-i18n';
import {formatDate} from '@/helpers/other';
import { Link } from '@inertiajs/vue3';

const {t} = useI18n();

// Helper function to format step numbers with leading zero
const formatStepNumber = (step: number): string => {
    return step.toString().padStart(2, '0');
};

const tempIconMap = {
    discussion: 'ti ti-message-circle',
    poll: 'ti ti-selector',
    survey: 'ti ti-user-question',
}

// Define props passed from Laravel
const props = defineProps<{
    procedure: any;
    procedures: {
        data: Procedure[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();
</script>
