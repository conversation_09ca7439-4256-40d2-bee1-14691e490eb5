<template>
    <div class="d-flex gap-2 flex-wrap">
        <div class="position-relative flex-grow-1" style="min-width: 200px;">
            <input
                type="text"
                class="form-control ps-4 pe-4"
                v-model="searchValue"
                :placeholder="searchPlaceholder"
            />
            <i class="ti ti-search position-absolute translate-middle-y top-50 ms-2"></i>
            <i
                v-if="searchValue?.length"
                @click="clearSearch"
                class="pq-cursor-pointer ti ti-x position-absolute translate-middle-y end-0 top-50 p-2"
            ></i>
        </div>

        <select
            v-if="selectOptions?.length"
            class="form-select"
            v-model="selectValue"
            style="min-width: 150px; width: auto;"
        >
            <option
                v-for="option in selectOptions"
                :key="option.value"
                :value="option.value"
            >
                {{ option.label }}
            </option>
        </select>
    </div>
</template>

<script setup lang="ts">
import { watch, ref } from 'vue';
import { router } from '@inertiajs/vue3';

interface SelectOption {
    value: string | number;
    label: string;
}

const props = defineProps<{
    initialSearchValue?: string;
    initialSelectValue?: string | number;
    searchPlaceholder: string;
    searchRoute: string;
    selectOptions?: SelectOption[];
    searchKey?: string;
    selectKey?: string;
}>();

const searchValue = ref(props.initialSearchValue || '');
const selectValue = ref(props.initialSelectValue || '');

let searchTimeout: number;

// Watch for changes in either search or select values
watch([searchValue, selectValue], ([newSearchValue, newSelectValue]) => {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
        const payload: Record<string, any> = {};

        // Add search key
        payload[props.searchKey || 'searchKey'] = newSearchValue;

        // Add select key if provided
        if (props.selectKey) {
            payload[props.selectKey] = newSelectValue;
        }

        console.log('payload', payload);

        router.post(route(props.searchRoute), payload);
    }, 500);
});

const clearSearch = () => {
    searchValue.value = '';
};
</script>
