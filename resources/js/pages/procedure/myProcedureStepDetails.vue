<template>
    <VerticalLayout>
<!--        Period: {{ procedureStep.starts_at }} - {{ procedureStep.ends_at }}
        <h3><a :href="procedureStep.link">linkie for step details</a></h3>

        <div>
            Total Messages: {{ procedureStep.comments_count }}
        </div>
        <div style="color:red" v-if="!procedureStep.isCurrent">Expired</div>
        <div style="color:blue" v-if="procedureStep.isCurrent">Current Running</div>
        <div>
            <strong>Description</strong>
            <br>
            <i>{{ procedureStep.description }}</i>
        </div>

        <h4>Comments</h4>
        <button>Add New (if is active)</button>

        <div v-for="comment in procedureStep.top_level_comments" :key="`comment_${comment.id}`">
            {{ comment.comment }} at {{ comment.created_at }} <strong>by</strong> {{ comment.user.name }} {{ comment.user.surname }}
            <div v-for="subcomment in comment.children_recursive" :key="`subcomment_${subcomment.id}`" style="padding-left:20px;">
                {{ subcomment.comment }} at {{ subcomment.created_at }} <strong>by</strong> {{ subcomment.user.name }}
                {{ subcomment.user.surname }}
                <button>Reply</button>
                <button>Delete (if that message belongs to him)</button>
            </div>
        </div>-->


        <PageTitle :subtitle="procedureStep.procedure.title" :title="procedureStep.description" icon="ti ti-bubble"
                   :subtitleLink="procedureStep.procedure.link"/>

        <b-row>
            <b-col xl="6" lg="12">
                <b-card no-body>
                    <b-card-body>
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <span v-if="!procedureStep.isCurrent" class="badge px-2 py-1 badge-soft-danger  fs-13 ms-1">Expired</span>
                                <span v-else class="badge px-2 py-1 badge-soft-success  fs-13 ms-1"> Ongoing </span>
                            </div>
                        </div>
                        <div class="mt-3 mb-1">
                            <a href="#!" class="text-dark fs-20 fw-medium">{{ procedureStep.description }}</a>
                        </div>
                        <p class="text-muted fw-medium fs-14 mb-1"><span class="text-dark">Time Period : </span> {{ formatDate(procedureStep.starts_at) }} - {{ formatDate(procedureStep.ends_at) }}</p>
                        <p class="text-muted fw-medium fs-14 mb-1"><span class="text-dark">Organizer : </span> Ministry of Environment</p>
                        <h4 class="text-dark fw-medium">Category :</h4>
                        <p class="mb-1">Environmental Policy / Community Engagement</p>
                        <h4 class="text-dark fw-medium">Description :</h4>
                        <p class="mb-1">
                            A public forum organized to invite citizens, stakeholders, and experts to provide feedback and suggestions on proposed
                            strategies and actions for the city's 2030 climate and sustainability roadmap. The purpose is to ensure that community
                            voices are included in policy development and that the final plan reflects local needs and priorities.
                        </p>
                        <p class="mb-1">
                            A public forum organized to invite citizens, stakeholders, and experts to provide feedback and suggestions on proposed
                            strategies and actions for the city's 2030 climate and sustainability roadmap. The purpose is to ensure that community
                            voices are included in policy development and that the final plan reflects local needs and priorities.
                        </p>
                        <a href="#!" class="link-primary">Read More...</a>
                        <h4 class="text-dark fw-medium mt-3">Details :</h4>
                        <ul class="d-flex flex-column fs-14 mb-0 gap-1">
                            <li>
                                <strong>Objective:</strong> Gather public feedback on proposed climate actions, targets, and policies for the city's
                                2030 sustainability roadmap.
                            </li>
                            <li><strong>Format:</strong> Presentations, Q&A session, breakout discussions with stakeholders</li>
                            <li>
                                <strong>How to Participate:</strong> Register online by 15 August or attend in person; virtual participation available
                                via livestream
                            </li>
                            <li><strong>Contact:</strong> <EMAIL></li>
                        </ul>

                        <h4 class="text-dark fw-medium mt-3 mb-2 pb-1">Additional Information :</h4>
                        <div class="rounded border border-dashed p-2 text-center">
                            <b-row>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 1 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 2 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 3 : </span> info text</p>
                                </b-col>
                                <b-col lg="3" cols="4" class="border-end">
                                    <p class="text-muted fw-medium fs-14 mb-0"><span class="text-dark">Info 4 : </span> info text</p>
                                </b-col>
                            </b-row>
                        </div>
                    </b-card-body>
                    <b-card-footer class="border-top border-dashed">
                        <b-row class="g-2">
                            <b-col lg="3">
                                <a href="#!" class="btn btn-primary d-flex align-items-center w-100 gap-1">
                                    <Icon icon="solar:cart-large-2-bold" class="fs-16 align-middle" />
                                    Button 1</a
                                >
                            </b-col>
                            <b-col lg="3">
                                <a href="#!" class="btn btn-success d-flex align-items-center w-100 gap-1">
                                    <Icon icon="solar:bag-check-bold" class="fs-16 align-middle" />
                                    Button 2</a
                                >
                            </b-col>
                            <b-col lg="3">
                                <a href="#!" class="btn btn-outline-danger d-flex align-items-center w-75 gap-1">
                                    <Icon icon="solar:heart-bold" class="fs-16 align-middle" />
                                    Button 3</a
                                >
                            </b-col>
                        </b-row>
                    </b-card-footer>
                </b-card>
            </b-col>
            <b-col xl="6" lg="12">
                <div class="card">
                    <div class="chat d-flex">
                        <Comments :comments="procedureStep.top_level_comments" />
                    </div>
                </div>
            </b-col>
            <b-col xl="12" lg="12">
                <b-card no-body>
                    <b-tabs content-class="m-3">
                        <b-tab title-item-class="p-2 pb-0">
                            <template v-slot:title>
                                <span class="d-block d-sm-none"><Icon icon="solar:notebook-bold" class="fs-20" /></span>
                                <span class="d-none d-sm-block"><Icon icon="solar:notebook-bold" class="fs-14 me-1 align-middle" /> other</span>
                            </template>
                        </b-tab>

                        <b-tab title-item-class="p-2 pb-0">
                            <template v-slot:title>
                                <span class="d-block d-sm-none"><Icon icon="solar:chat-dots-bold" class="fs-20" /></span>
                                <span class="d-none d-sm-block"><Icon icon="solar:chat-dots-bold" class="fs-14 me-1 align-middle" /> Other</span>
                            </template>
                            test
                        </b-tab>
                    </b-tabs>
                </b-card>
            </b-col>
        </b-row>


    </VerticalLayout>
</template>

<script setup lang="ts">
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {Procedure} from '@/types/procedure';
import {useI18n} from 'vue-i18n';
import Comments from '@/components/Comments.vue';
import PageTitle from '@/components/PageTitle.vue';
import { Icon } from '@iconify/vue';
import {formatDate} from '@/helpers/other';

const {t} = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    procedureStep: any;
    procedures: {
        data: Procedure[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

</script>
