<template>
    <VerticalLayout>
        <PageTitle title="Test Consultation Steps" subtitle="Rooms" />
        <b-row>
            <b-col>
                <b-card no-body>
                    <b-card-body>


                        <draggable
                            v-model="steps"
                            class="list-group"
                            tag="ul"
                            v-bind="dragOptions"
                            group="steps"
                            handle=".handle"
                            :model-type="dateFormat"
                            :format="dateFormat"
                            @start="drag=true"
                            @end="drag=false"
                            item-key="id">
                            <template #item="{element}">
                                <li :key="element.id" class="list-group-item d-flex align-items-center gap-2 mb-2 p-1 border rounded border-solid"
                                     :class="{'bg-danger-subtle': isStartAfterEnd(element.start, element.end)}">
                                    <i class="ti ti-grip-vertical handle p-2"></i>
                                    <span class="flex-shrink-0">{{ element.title }}</span>
                                    <VueDatePicker
                                        :enable-time-picker="false"
                                        :model-type="dateFormat"
                                        :format="format"
                                        :locale="locale"
                                        auto-apply
                                        v-model="element.start"
                                        :key="`start-${element.id}`"
                                        placeholder="Select start date"
                                        class="flex-grow-0"
                                    />
                                    <VueDatePicker
                                        :enable-time-picker="false"
                                        :model-type="dateFormat"
                                        :format="format"
                                        :locale="locale"
                                        auto-apply
                                        v-model="element.end"
                                        :key="`end-${element.id}`"
                                        placeholder="Select end date"
                                        class="flex-grow-0"
                                    />
                                </li>
                            </template>
                        </draggable>


<!--                        <div v-for="(step, idx) in steps" :key="step.id" class="d-flex align-items-center gap-2 mb-4 p-2 border rounded border-solid"
                            :class="{'bg-danger-subtle': isStartAfterEnd(step.start, step.end)}">
                            <span class="flex-shrink-0">{{ step.title }}</span>
                            <VueDatePicker
                                :enable-time-picker="false"
                                :model-type="dateFormat"
                                :locale="locale"
                                auto-apply
                                v-model="step.start"
                                :key="`start-${step.id}`"
                                placeholder="Select start date"
                                class="flex-grow-0"
                            />
                            <VueDatePicker
                                :enable-time-picker="false"
                                :model-type="dateFormat"
                                :locale="locale"
                                auto-apply
                                v-model="step.end"
                                :key="`end-${step.id}`"
                                placeholder="Select end date"
                                class="flex-grow-0"
                            />
                        </div>-->

<!--                        <pre>
                            daysBetweenFirstStartAndLastEnd: {{daysBetweenFirstStartAndLastEnd}}
                        </pre>-->
                        <pre>
                            {{ steps }}
                        </pre>

                    </b-card-body>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import { computed, ref, watch } from 'vue';
import VueDatePicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css'
import { BCard, BCardBody, BCol, BRow } from 'bootstrap-vue-next';
import { useI18n } from 'vue-i18n';
const { locale } = useI18n();
import draggable from 'vuedraggable';

const props = defineProps<{

}>();

const dateFormat = 'yyyy-MM-dd';
const format = (date: Date) => {
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
}
const drag = ref(false);


interface Step {
    id: number;
    start: Date | null;
    end: Date | null;
    title: string;
}

const steps = ref<Step[]>([
    { id: 1, start: null, end: null, title: 'Step 1' },
    { id: 2, start: null, end: null, title: 'Step 2' },
    { id: 3, start: null, end: null, title: 'Step 3' },
    { id: 4, start: null, end: null, title: 'Step 4' },
]);

/*function updateStepDate(id: number, date: string, isStart: boolean) {
    const step = steps.value.find(p => p.id === id);
    if (step) {
        isStart ? step.start = date : step.end = date;
    }
}*/

function isStartAfterEnd(start: Date | null, end:  Date | null): boolean {
    if (start && end) {
        console.log('isStartAfterEnd', start, end, start > end);
        return start > end;
    }
    return false;
}

const dragOptions = computed(() => ({
    animation: 200,
    group: "steps",
    disabled: false,
    ghostClass: "ghost"
}));

/*const daysBetweenFirstStartAndLastEnd = computed(() => {
    if (steps.value.length === 0) return null;

    const firstStart = steps.value[0].start;
    const lastEnd = steps.value[steps.value.length - 1].end;

    if (!firstStart || !lastEnd) return null;

    // Calculate the difference in time (milliseconds)
    const diffTime = lastEnd.getTime() - firstStart.getTime();

    // Convert milliseconds to days (rounded)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays >= 0 ? diffDays : null; // return null if the end is before start
});*/

watch(
    () => steps.value.map(step => [step.start, step.end]),
    (newValues) => {
        newValues.forEach((dates, index) => {
            const step = steps.value[index];
            // console.log(`Step ${step.id} changed:`, step);
        });
    },
    { deep: true }
);



</script>


<style>
.flip-list-move {
    transition: transform 0.5s;
}

.no-move {
    transition: transform 0s;
}

.ghost {
    opacity: 0.5;
    background: #c8ebfb;
}
</style>
