<template>
    <VerticalLayout>
        <PageTitle icon="ti ti-users-group" :title="t('userGroups.title')" :subtitle="t('userGroups.list')" subtitleRoute="userGroupIndex"/>

        <b-row>
            <b-col cols="12">
                <b-card no-body>
                    <b-card-header class="border-bottom border-light">
                        <div class="d-flex justify-content-between flex-wrap gap-2">
                            <SearchInput :initialValue="searchInput" searchRoute="userGroupIndexPost"
                                         :placeholder="t('userGroups.search')"/>
                            <div>
                                <Link :href="route('userGroupAdd')" class="btn btn-primary"><i
                                    class="ti ti-plus me-1"></i>{{ t('userGroups.add') }}
                                </Link>
                            </div>
                        </div>
                    </b-card-header>

                    <b-table-simple responsive hover class="mb-0 text-nowrap">
                        <b-thead class="bg-light-subtle">
                            <b-tr>
                                <b-th class="ps-3" style="width: 50px">
                                    <b-form-checkbox :checked="allSelected" @change="toggleSelectAll" />
                                </b-th>
                                <b-th>ID</b-th>
                                <b-th><i class="ti ti-article fs-16 me-1 align-text-top" />{{ t('common.title') }}</b-th>
                                <b-th><i class="ti ti-user fs-16 me-1 align-text-top" />{{ t('common.admin') }}</b-th>
                                <b-th><i class="ti ti-users fs-16 me-1 align-text-top" />{{ t('users.title') }}</b-th>
                                <b-th><i class="ti ti-tags fs-16 me-1 align-text-top" />{{ t('tags.title') }}</b-th>
                                <b-th class="text-center" style="width: 120px"
                                    ><i class="ti ti-bolt fs-16 me-1 align-text-top" />{{ t('common.actions') }}</b-th
                                >
                            </b-tr>
                        </b-thead>
                        <b-tbody>
                            <b-tr v-for="userGroup in userGroups.data" :key="`userGroup_${userGroup.id}`">
                                <b-td class="ps-3">
                                    <b-form-checkbox :value="userGroup.id" v-model="selectedRows"/>
                                </b-td>
                                <b-td>{{ userGroup.id }}</b-td>
                                <b-td>
                                    <Link variant="info" v-b-tooltip.hover.top="t('userGroups.edit')"
                                          :href="route('updateUserGroup', { id: userGroup.id })">{{
                                            userGroup.title
                                    }}</Link>
                                </b-td>
                                <b-td>{{ userGroup.admin.name }}</b-td>
                                <b-td>{{ userGroup.users.map((user) => user.name).join(', ') }}</b-td>
                                <b-td>{{ userGroup.tags.map((tag) => tag.title).join(', ') }}</b-td>

                                <b-td class="pe-3">
                                    <div class="hstack justify-content-end gap-1">
                                        <Link :href="route('updateUserGroup', { id: userGroup.id })"
                                              class="btn btn-soft-success btn-icon btn-sm rounded-circle"
                                            ><i class="ti ti-edit fs-16"></i
                                        ></Link>

                                        <button @click="deleteWithConfirm(userGroup.id)"
                                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle">
                                            <i class="ti ti-trash"></i>
                                        </button>
                                    </div>
                                </b-td>
                            </b-tr>
                        </b-tbody>
                    </b-table-simple>

                    <b-card-footer class="d-flex justify-content-between align-items-center">
                        <div>
                            <button
                                v-if="selectedRows.length"
                                v-b-tooltip.hover.top="t('common.bulkDelete')"
                                @click="deleteMultipleWithConfirm"
                                class="btn btn-soft-danger btn-icon btn-sm rounded-circle"
                            >
                                <i class="ti ti-trash"></i>
                            </button>
                        </div>
                        <Pagination
                            :items="props.userGroups"
                        />
                    </b-card-footer>
                </b-card>
            </b-col>
        </b-row>
    </VerticalLayout>
</template>

<script setup lang="ts">
import PageTitle from '@/components/PageTitle.vue';
import VerticalLayout from '@/layouts/VerticalLayout.vue';
import {useDeleteActions} from '@/composables/useDeleteActions';
import {UserGroup} from '@/types/userGroup';
import {Link, usePage} from '@inertiajs/vue3';
import {
    BCard,
    BCardFooter,
    BCardHeader,
    BCol,
    BFormCheckbox,
    BRow,
    BTableSimple,
    BTbody,
    BTd,
    BTh,
    BThead,
    BTr,
    vBTooltip
} from 'bootstrap-vue-next';
import {ref} from 'vue';
import {useI18n} from 'vue-i18n';
import Pagination from '@/components/Pagination.vue';
import SearchInput from '@/components/SearchInput.vue';

const { t } = useI18n();

// Define props passed from Laravel
const props = defineProps<{
    searchKey?: string;
    userGroups: {
        data: UserGroup[];
        current_page: number;
        per_page: number;
        total: number;
        links: Array<{
            url: string | null;
            label: string;
            active: boolean;
        }>;
    };
}>();

// Delete Actions Composable
const {
    selectedRows,
    allSelected,
    toggleSelectAll,
    deleteWithConfirm,
    deleteMultipleWithConfirm
} = useDeleteActions(
    () => props.userGroups.data,
    {
        entityName: 'userGroup',
        deleteRoute: 'deleteUserGroup',
        massDeleteRoute: 'massDeleteUserGroups',
        successMessage: 'alerts.userGroups.delete.success',
        errorMessage: 'alerts.userGroups.delete.error',
        bulkSuccessMessage: 'alerts.userGroups.delete.successBulk',
        bulkErrorMessage: 'alerts.userGroups.delete.errorBulk',
        bulkDeleteTranslationKey: 'userGroup4s.bulkDelete'
    }
);

// Search Input
const searchInput = ref(usePage().props.searchKey as string || '');

</script>
