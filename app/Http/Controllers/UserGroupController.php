<?php

namespace App\Http\Controllers;

use App\DTO\Requests\TagRequestDTO;
use App\DTO\Requests\UserGroupRequestDTO;
use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\UserGroup\AddUserGroupRequest;
use App\Http\Requests\UserGroup\UpdateUserGroupRequest;
use App\Services\TagService;
use App\Services\UserGroupService;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class UserGroupController extends MainController
{
    private UserGroupService $userGroupService;
    private TagService $tagService;
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userGroupService = new UserGroupService();
        $this->tagService = new TagService();
        $this->userService = new UserService();

        // Auth role protection for the whole controller
        $this->middleware('role:administrator');
    }

    public function index(): Response
    {
        $this->data['searchKey'] = Session::get('userGroup_searchKey') ?? '';

        $userGroupRequestDTO = new UserGroupRequestDTO();
        $userGroupRequestDTO->searchKey = $this->data['searchKey'];
        $userGroupRequestDTO->with = ['admin', 'tags', 'users'];
        $this->data['userGroups'] = $this->userGroupService->getUserGroups($userGroupRequestDTO);

        return Inertia::render('userGroup/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        Session::put('userGroup_searchKey', (request()->post()['searchKey'] ?? ''));

        return response()->redirectToRoute('userGroupIndex', setLocaleParams());
    }

    public function deleteUserGroup(int $id): RedirectResponse
    {
        $this->userGroupService->deleteUserGroup($id);
        return redirect()->back();
    }

    public function addUserGroup(): Response
    {
        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->all = true;
        $this->data['tags'] = $this->tagService->getTags($tagRequestDTO);

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->all = true;
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('userGroup/details', $this->data);
    }

    public function addUserGroupPost(AddUserGroupRequest $addUserGroupRequest): RedirectResponse
    {
        $data = $addUserGroupRequest->validated();
        $userGroup = $this->userGroupService->upsert($data);

        return response()->redirectToRoute('updateUserGroup', setLocaleParams(['id' => $userGroup->id]));
    }

    public function updateUserGroup(): Response
    {
        $userGroupRequestDTO = new UserGroupRequestDTO();
        $userGroupRequestDTO->id = request()->route('id');
        $userGroupRequestDTO->with = ['tags', 'users'];
        $this->data['userGroup'] = $this->userGroupService->getUserGroup($userGroupRequestDTO);

        $tagRequestDTO = new TagRequestDTO();
        $tagRequestDTO->all = true;
        $this->data['tags'] = $this->tagService->getTags($tagRequestDTO);

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->all = true;
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('userGroup/details', $this->data);
    }

    public function updateUserGroupPost(UpdateUserGroupRequest $updateUserGroupRequest): RedirectResponse
    {
        $this->userGroupService->upsert($updateUserGroupRequest->validated());

        return redirect()
            ->back()
            ->with('success', 'UserGroup updated successfully.');
    }

}
