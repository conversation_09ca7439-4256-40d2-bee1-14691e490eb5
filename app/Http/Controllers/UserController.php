<?php

namespace App\Http\Controllers;

use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\Register\RegisterRequest;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends MainController
{
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    public function register(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return redirect()->route('dashboard', ['language' => 'el']);
        }

				if (auth()->check()) { // If public registration not enabled for this installation redirect to login
            return response()->redirectToRoute('login');
        }

        return Inertia::render('register/Register', $this->data);
    }

    public function registerPost(RegisterRequest $registerRequest): RedirectResponse
    {
        if (auth()->check()) {
            return redirect()->route('dashboard', ['language' => 'el']);
        }
        $credentials = $registerRequest->validated();
        if ( $this->userService->upsert($credentials) == null )
            return back()->withErrors([
                'email' => 'Invalid credentials provided.'
            ])->withInput();

        return redirect()->route('dashboard', ['language' => 'el']);

        //---
        //Log::debug('hello!');
        //Log::debug(print_r($credentials, true));
        //---
    }

    public function index(): Response
    {
        $this->data['searchKey'] = Session::get('user_searchKey') ?? '';

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->searchKey = $this->data['searchKey'];
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('user/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        Session::put('user_searchKey', (request()->post()['searchKey'] ?? ''));
        return response()->redirectToRoute('userIndex', setLocaleParams());
    }

    /**
     * Handle user filtering and return JSON response
     * This method can be used for AJAX requests or API calls
     */
    public function indexJson(Request $request): JsonResponse
    {
        $userRequestDTO = new UserRequestDTO();

        // Set search parameters from the request
        $userRequestDTO->searchKey = $request->input('searchKey', '');
        $userRequestDTO->isActive = $request->boolean('isActive');
        $userRequestDTO->status = $request->input('status');
        $userRequestDTO->roles = $request->input('roles', []);
        $userRequestDTO->rpp = $request->input('rpp', 20);
        $userRequestDTO->all = $request->boolean('all', false);

        // Handle ordering
        if ($request->has('orderBy')) {
            $userRequestDTO->orderBy = $request->input('orderBy', []);
        }

        // Get filtered users
        $users = $this->userService->getUsers($userRequestDTO);

        return response()->json([
            'success' => true,
            'data' => $users,
            'message' => 'Users retrieved successfully'
        ]);
    }
}
