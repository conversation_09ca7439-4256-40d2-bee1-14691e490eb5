<?php

namespace App\Http\Controllers;

use App\DTO\Requests\UserRequestDTO;
use App\Http\Requests\Register\RegisterRequest;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends MainController
{
    private UserService $userService;

    public function __construct()
    {
        parent::__construct();
        $this->userService = new UserService();
    }

    public function register(): Response|RedirectResponse
    {
        if (auth()->check()) {
            return redirect()->route('dashboard', ['language' => 'el']);
        }

				if (auth()->check()) { // If public registration not enabled for this installation redirect to login
            return response()->redirectToRoute('login');
        }

        return Inertia::render('register/Register', $this->data);
    }

    public function registerPost(RegisterRequest $registerRequest): RedirectResponse
    {
        if (auth()->check()) {
            return redirect()->route('dashboard', ['language' => 'el']);
        }
        $credentials = $registerRequest->validated();
        if ( $this->userService->upsert($credentials) == null )
            return back()->withErrors([
                'email' => 'Invalid credentials provided.'
            ])->withInput();

        return redirect()->route('dashboard', ['language' => 'el']);

        //---
        //Log::debug('hello!');
        //Log::debug(print_r($credentials, true));
        //---
    }

    public function index(): Response
    {
        $this->data['searchKey'] = Session::get('user_searchKey') ?? '';

        $userRequestDTO = new UserRequestDTO();
        $userRequestDTO->searchKey = $this->data['searchKey'];
        $this->data['users'] = $this->userService->getUsers($userRequestDTO);

        return Inertia::render('user/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        Session::put('user_searchKey', (request()->post()['searchKey'] ?? ''));
        return response()->redirectToRoute('userIndex', setLocaleParams());
    }
}
