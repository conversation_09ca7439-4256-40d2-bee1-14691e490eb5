<?php

namespace App\Http\Controllers;

use App\DTO\Requests\ProcedureRequestDTO;
use App\DTO\Requests\ProcedureStepRequestDTO;
use App\DTO\Requests\UserGroupRequestDTO;
use App\Http\Requests\Procedures\AddProcedureRequest;
use App\Http\Requests\Procedures\MassDeleteProcedureRequest;
use App\Http\Requests\Procedures\UpdateProcedureRequest;
use App\Services\ProcedureService;
use App\Services\UserGroupService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;
use Inertia\Response;

class ProcedureController extends MainController
{
    private ProcedureService $procedureService;

    private UserGroupService $userGroupService;

    public function __construct()
    {
        parent::__construct();
        $this->procedureService = new ProcedureService();
        $this->userGroupService = new UserGroupService();
    }

    public function index(): Response
    {
        abort_unless(
            auth()
                ->user()
                ->hasR<PERSON>('administrator'),
            403
        );

        $this->data['searchKey'] = Session::get('procedure_searchKey') ?? '';
        $this->data['activePeriod'] = Session::get('procedure_activePeriod') ?? null;

        $procedureRequestDTO = new ProcedureRequestDTO();
        $procedureRequestDTO->searchKey = $this->data['searchKey'];
        if ($this->data['activePeriod'] == 1) {
            $procedureRequestDTO->activePeriod = true;
        }
        if ($this->data['activePeriod'] == 0 && !is_null($this->data['activePeriod'])) {
            $procedureRequestDTO->activePeriod = false;
        }

        $procedureRequestDTO->with = ['steps'];

        $this->data['procedures'] = $this->procedureService->getProcedures($procedureRequestDTO);

        return Inertia::render('procedure/index', $this->data);
    }

    public function indexPost(): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        Session::put('procedure_searchKey', (request()->post()['searchKey'] ?? ''));
        Session::put('procedure_activePeriod', (request()->post()['activePeriod'] ?? null));

        return response()->redirectToRoute('procedureIndex', setLocaleParams());
    }

    public function deleteProcedure(int $id): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $this->procedureService->deleteProcedure($id);

        return redirect()->back();
    }

    public function massDeleteProcedures(MassDeleteProcedureRequest $massDeleteProcedureRequest): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $procedureIds = $massDeleteProcedureRequest->validated()['ids'];
        $this->procedureService->massDelete($procedureIds);

        return redirect()->back();
    }

    public function addProcedure(): Response
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        // todo: do we need usergroups here?
        $userGroupRequestDTO = new UserGroupRequestDTO();
        $userGroupRequestDTO->all = true;
        $this->data['userGroups'] = $this->userGroupService->getUserGroups($userGroupRequestDTO);

        return Inertia::render('procedure/details', $this->data);
    }

    public function addProcedurePost(AddProcedureRequest $addProcedureRequest): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $data = $addProcedureRequest->validated();
        $procedure = $this->procedureService->upsert($data);

        return response()->redirectToRoute('updateProcedure', setLocaleParams(['id' => $procedure->id]));
    }

    public function updateProcedure(): Response
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $procedureRequestDTO = new ProcedureRequestDTO();
        $procedureRequestDTO->with = ['steps', 'steps.userGroups', 'steps.users'];
        $procedureRequestDTO->id = request()->route('id');
        $this->data['procedure'] = $this->procedureService->getProcedure($procedureRequestDTO);

        return Inertia::render('procedure/details', $this->data);
    }

    public function updateProcedurePost(UpdateProcedureRequest $updateProcedureRequest): RedirectResponse
    {
        abort_unless(
            auth()
                ->user()
                ->hasRole('administrator'),
            403
        );

        $this->procedureService->upsert($updateProcedureRequest->validated());

        return redirect()
            ->back()
            ->with('success', 'Procedure updated successfully.');
    }

    public function myProcedures(): Response
    {
        $procedureRequestDTO = new ProcedureRequestDTO();
        if (!auth()
            ->user()
            ->hasRole('administrator')) {
            $procedureRequestDTO->mine = true;
        }

        $procedureRequestDTO->with = ['steps', 'steps.userGroups', 'steps.users'];
        $this->data['procedures'] = $this->procedureService->getProcedures($procedureRequestDTO);

        return Inertia::render('procedure/myProcedures', $this->data);
    }

    public function myProcedureDetails(): Response
    {
        $procedureRequestDTO = new ProcedureRequestDTO();
        $procedureRequestDTO->id = request()->route('id');
        if (!auth()
            ->user()
            ->hasRole('administrator')) {
            $procedureRequestDTO->mine = true;
        }
        $procedureRequestDTO->with = ['steps', 'steps.userGroups', 'steps.users'];
        $procedureRequestDTO->withThreadsCount = true;
        $this->data['procedure'] = $this->procedureService->getProcedure($procedureRequestDTO);

        return Inertia::render('procedure/myProcedureDetails', $this->data);
    }

    public function myProcedureStepDetails(): Response
    {
        $procedureStepRequestDTO = new ProcedureStepRequestDTO();
        $procedureStepRequestDTO->id = request()->route('id');
        $procedureStepRequestDTO->withCommentsCount = true;
        $procedureStepRequestDTO->with = ['procedure', 'threads'];

        $this->data['procedureStep'] = $this->procedureService->getProcedureStep($procedureStepRequestDTO);

        $userHasAccessToUserGroupIds = auth()
            ->user()
            ->hasAccessToUserGroupIds();
        $procedureStepGroupIds = $this->data['procedureStep']->userGroups();

        // Check where a user has access through userGroups or directly.
        if (!auth()
            ->user()
            ->hasRole('administrator')) {
            if ((!in_array(
                    auth()->id(),
                    $this->data['procedureStep']->users->pluck('id')
                        ->toArray()
                )) && empty(array_intersect($userHasAccessToUserGroupIds, $procedureStepGroupIds))) {
                abort(403);
            }
        }

        return Inertia::render('procedure/myProcedureStepDetails', $this->data);
    }


}
