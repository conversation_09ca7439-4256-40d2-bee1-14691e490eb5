<?php

namespace App\Http\Requests\UserGroup;

use Illuminate\Foundation\Http\FormRequest;

class UpdateUserGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'id' => 'required|integer|exists:user_groups,id',
            'title'       => 'required|string',
            'description' => 'required|string',
            'adminId'     => 'required|numeric|exists:users,id',
            'tagIds'      => 'required|array',
            'tagIds.*'    => 'required|numeric|exists:tags,id',
            'userIds'     => 'required|array',
            'userIds.*'   => 'required|numeric|exists:users,id',
        ];
    }
}
