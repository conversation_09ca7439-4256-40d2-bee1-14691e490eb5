<?php

namespace App\Http\Requests\Procedures;

use App\Rules\ValidProcedureSteps;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class AddProcedureRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'title'               => 'required|string',
            'description'         => 'nullable|string',
            'steps'               => [
                'nullable',
                'array',
                new ValidProcedureSteps()
            ],
            'steps.*.starts_at'   => 'required|date|date_format:Y-m-d H:i:s',
            'steps.*.ends_at'     => 'required|date|date_format:Y-m-d H:i:s|after:userGroups.*.starts_at',
            'steps.*.description' => 'nullable|string',
            'steps.*.type'        => 'required|in:'.implode(',', config('procedures.steps.types')),
            'steps.*.userGroupIds'   => 'array',
            'steps.*.userGroupIds.*' => 'integer|exists:user_groups,id',
            'steps.*.userIds'        => 'array',
            'steps.*.userIds.*'      => 'integer|exists:users,id',
        ];
    }

    protected function failedValidation(Validator $validator): void
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'errors'  => $validator->errors(),
        ], 422));
    }
}
