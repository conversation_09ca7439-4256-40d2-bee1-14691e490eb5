<?php

namespace App\Models;

use App\Scopes\DeletedScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class UserGroup extends Model
{
    /** @use HasFactory<\Database\Factories\UserGroupFactory> */
    use HasFactory;

    protected $fillable = ['title', 'description', 'adminId', 'isDeleted'];

    protected static function booted(): void
    {
        static::addGlobalScope(new DeletedScope());
    }

    public function admin(): BelongsTo
    {
        return $this->belongsTo(User::class, 'adminId');
    }

    public function procedureSteps(): BelongsToMany
    {
        return $this->belongsToMany(
            ProcedureStep::class,
            'procedure_step_user_groups',
            'userGroupId',
            'procedureStepId'
        )
            ->withTimestamps();
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'user_group_tags', 'userGroupId', 'tagId')
            ->withTimestamps();
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_group_users', 'userGroupId', 'userId')
            ->withTimestamps();
    }

    public function procedures(): BelongsToMany
    {
        return $this->belongsToMany(Procedure::class, 'procedure_steps', 'userGroupId', 'procedureId')
            ->withPivot(['starts_at', 'ends_at', 'description'])
            ->withTimestamps();
    }
}
