<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, CanResetPassword, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'surname',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function userGroups(): BelongsToMany
    {
        return $this->belongsToMany(UserGroup::class, 'user_group_users', 'userId', 'userGroupId');
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'user_tags', 'userId', 'tagId')
            ->withTimestamps();
    }

    public function procedureSteps(): BelongsToMany
    {
        return $this->belongsToMany(
            ProcedureStep::class,
            'procedure_step_users',
            'userId',
            'procedureStepId'
        )
            ->withTimestamps();
    }

    public function hasAccessToUserGroupIds(): array
    {
        $userGroups = [];
        $userGroupsWithDirectAccess = UserGroupUser::where('userId', $this->id)
            ->pluck('userGroupId')
            ->toArray();
        $userTagIds = $this->tags->pluck('id')
            ->toArray();
        $userGroupsWithTagAccess = UserGroupTag::whereIn('tagId', $userTagIds)
            ->pluck('userGroupId')
            ->toArray();

        return array_merge($userGroups, $userGroupsWithDirectAccess, $userGroupsWithTagAccess);
    }
}
