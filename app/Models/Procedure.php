<?php

namespace App\Models;

use App\Scopes\DeletedScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

/**
 * @property int $id
 */
class Procedure extends Model
{
    /** @use HasFactory<\Database\Factories\ProcedureFactory> */
    use HasFactory;

    protected $fillable = ['title', 'description', 'isDeleted'];

    protected $appends = ['link'];

    protected static function booted(): void
    {
        static::addGlobalScope(new DeletedScope());
    }

    public function steps(): HasMany
    {
        return $this->hasMany(ProcedureStep::class, 'procedureId');
    }

    //// This relation is used to sync the userGroups (write endpoints)
    //public function procedureSteps(): BelongsToMany
    //{
    //    return $this->belongsToMany(\App\Models\UserGroup::class, 'procedure_steps', 'procedureId', 'userGroupId')
    //        ->withPivot(['id', 'starts_at', 'ends_at', 'description'])
    //        ->withTimestamps();
    //}

    public function getLinkAttribute(): string
    {
        return route('myProcedureDetails', setLocaleParams(['id' => $this->id, 'slug' => Str::slug($this->title ?? '-')]));
    }
}
