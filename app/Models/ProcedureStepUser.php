<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProcedureStepUser extends Model
{
    /** @use HasFactory<\Database\Factories\ProcedureStepUserFactory> */
    use HasFactory;

    protected $fillable = ['procedureStepId', 'userId'];

    public function procedureStep(): BelongsTo
    {
        return $this->belongsTo(ProcedureStep::class, 'procedureStepId');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'userId');
    }
}
