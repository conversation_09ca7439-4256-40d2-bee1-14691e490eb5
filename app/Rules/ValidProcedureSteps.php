<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Carbon;

class ValidProcedureSteps implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function __construct()
    {
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // allow null (handled by 'nullable|array' in base rules)
        if ($value === null) {
            return;
        }

        if (!is_array($value)) {
            $fail('The :attribute must be an array.');
            return;
        }

        // collect only well-formed intervals
        $intervals = [];
        foreach ($value as $i => $row) {
            if (!isset($row['starts_at'], $row['ends_at'])) {
                // base rules will already report missing fields
                continue;
            }

            try {
                $start = Carbon::parse($row['starts_at']);
                $end = Carbon::parse($row['ends_at']);
            } catch (\Throwable) {
                // base date_format rule will handle parse errors
                continue;
            }

            // Skip obviously invalid intervals; base rule will flag them
            if ($end->lt($start)) {
                continue;
            }

            // Keep index for friendly messages
            $intervals[] = ['idx' => $i, 'start' => $start, 'end' => $end];
        }

        if (count($intervals) < 2) return;

        // sort by start time
        usort($intervals, fn($a, $b) => $a['start'] <=> $b['start']);

        // detect overlaps (strict)
        for ($j = 1; $j < count($intervals); $j++) {
            $prev = $intervals[$j - 1];
            $curr = $intervals[$j];

            // overlap if curr.start < prev.end (edges touching are OK)
            if ($curr['start']->lt($prev['end'])) {
                $a = $prev['idx'] + 1;
                $b = $curr['idx'] + 1;
                $fail("userGroups time windows #{$a} and #{$b} overlap. Steps must not overlap.");
                // no early return; report all overlaps in this pass
            }
        }
    }
}
