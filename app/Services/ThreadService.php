<?php

namespace App\Services;

use App\Models\Thread;
use App\Repositories\ThreadRepository;

class ThreadService
{
    private ThreadRepository $threadRepository;

    public function __construct()
    {
        $this->threadRepository = new ThreadRepository();
    }

    public function deleteThread(int $threadId): void
    {
        $this->threadRepository->deleteThread($threadId);
    }

    public function upsert(array $data): Thread
    {
        return $this->threadRepository->upsert($data);
    }
}
