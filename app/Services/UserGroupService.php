<?php

namespace App\Services;

use App\DTO\Requests\UserGroupRequestDTO;
use App\Models\UserGroup;
use App\Repositories\UserGroupRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class UserGroupService
{
    private UserGroupRepository $userGroupRepository;

    public function __construct()
    {
        $this->userGroupRepository = new UserGroupRepository();
    }

    public function getUserGroups(UserGroupRequestDTO $userGroupRequestDTO): LengthAwarePaginator|Collection
    {
        return $this->userGroupRepository->getUserGroups($userGroupRequestDTO);
    }

    public function getUserGroup(UserGroupRequestDTO $userGroupRequestDTO): ?UserGroup
    {
        return $this->userGroupRepository->getUserGroups($userGroupRequestDTO)[0] ?? null;
    }

    public function upsert(array $data): UserGroup
    {
        return $this->userGroupRepository->upsert($data);
    }

    public function deleteUserGroup(int $userGroupId): void
    {
        $this->userGroupRepository->deleteUserGroup($userGroupId);
    }

}
