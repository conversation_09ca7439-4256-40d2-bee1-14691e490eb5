<?php

namespace App\Repositories;

use App\Models\Thread;

class ThreadRepository
{
    public function deleteThread(int $threadId): void
    {
        Thread::find($threadId)
            ->delete();
    }

    public function upsert(array $data): Thread
    {
        if ($data['id'] ?? null) {
            $thread = Thread::find($data['id']);
        } else {
            $thread = new Thread();
        }

        $thread->fill($data);
        $thread->save();

        return $thread;
    }
}
