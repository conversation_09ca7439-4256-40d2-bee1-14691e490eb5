<?php

namespace App\Repositories;

use App\DTO\Requests\UserGroupRequestDTO;
use App\Models\UserGroup;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class UserGroupRepository
{
    public function getUserGroups(UserGroupRequestDTO $userGroupRequestDTO): LengthAwarePaginator|Collection
    {
        $query = UserGroup::query();

        if ($userGroupRequestDTO->id) {
            $query->where('id', $userGroupRequestDTO->id);
        }

        if (count($userGroupRequestDTO->with)) {
            $query->with($userGroupRequestDTO->with);
        }

        if ($userGroupRequestDTO->searchKey) {
            $query->where('title', 'like', '%'.$userGroupRequestDTO->searchKey.'%')
                ->orWhere('description', 'like', '%'.$userGroupRequestDTO->searchKey.'%');
        }

        $query->{$userGroupRequestDTO->order === 'asc' ? 'orderBy' : 'orderByDesc'}($userGroupRequestDTO->orderBy);

        if ($userGroupRequestDTO->all) {
            return $query->get();
        }

        return $query->paginate($userGroupRequestDTO->rpp);
    }

    public function upsert(array $data): UserGroup
    {
        if ($data['id'] ?? null) {
            $userGroup = UserGroup::find($data['id']);
        } else {
            $userGroup = new UserGroup();
        }

        $userGroup->fill($data);
        $userGroup->save();

        $userGroup->tags()
            ->sync($data['tagIds']);
        $userGroup->users()
            ->sync($data['userIds']);

        return $userGroup;
    }

    public function deleteUserGroup(int $userGroupId): void
    {
        UserGroup::find($userGroupId)
            ->update(['isDeleted' => true]);
    }
}
