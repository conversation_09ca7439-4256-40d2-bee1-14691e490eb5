<?php

namespace App\Repositories;

use App\DTO\Requests\ProcedureRequestDTO;
use App\DTO\Requests\ProcedureStepRequestDTO;
use App\Models\Procedure;
use App\Models\ProcedureStep;
use App\Models\UserGroup;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ProcedureRepository
{
    public function getProcedureSteps(ProcedureStepRequestDTO $procedureStepRequestDTO): LengthAwarePaginator|Collection
    {
        $query = ProcedureStep::query();

        if ($procedureStepRequestDTO->id) {
            $query->where('id', $procedureStepRequestDTO->id);
        }

        if ($procedureStepRequestDTO->with) {
            $query->with($procedureStepRequestDTO->with);
        }

        if ($procedureStepRequestDTO->withCommentsCount === true) {
            $query->with([
                'threads' => function ($q) {
                    $q->withCount([
                        // counts only comments where comments.commentId IS NULL
                        'comments as top_level_comments_count' => function ($cq) {
                            $cq->whereNull('commentId');
                        },
                    ]);
                    $q->withCount('comments'); // adds comments_count on each step
                }
            ]);
        }

        return $query->paginate($procedureStepRequestDTO->rpp);
    }

    public function getComments()
    {
        //if ($procedureStepRequestDTO->withThreads) {
        //    $query->with([
        //        'topLevelComments' => function ($q) {
        //            $q->with(['user', 'childrenRecursive.user'])   // deep load
        //            ->orderBy('created_at');
        //        },
        //    ]);
        //}
    }

    public function getProcedures(ProcedureRequestDTO $procedureRequestDTO): LengthAwarePaginator|Collection
    {
        $now = now();
        $query = Procedure::query();

        if ($procedureRequestDTO->id) {
            $query->where('procedures.id', $procedureRequestDTO->id);
        }

        if (count($procedureRequestDTO->with)) {
            $query->with($procedureRequestDTO->with);
        }

        if ($procedureRequestDTO->withThreadsCount === true) {
            $query->with([
                'steps' => function ($q) {
                    $q->withCount('threads'); // adds threads_count on each step
                }
            ]);
        }

        if ($procedureRequestDTO->mine === true) {
            $userId = auth()->id();
            $tags = auth()->user()->tags->pluck('id')
                ->toArray();

            $groupIds = auth()
                ->user()
                ->userGroups()
                ->pluck('user_groups.id')
                ->all(); // usergroups the user directly belongs
            $userGroupsWithTag = UserGroup::whereHas('tags', function ($q) use ($tags) { // usergroups from tags
                $q->whereIn('tags.id', $tags);
            })
                ->get()
                ->pluck('id')
                ->toArray();
            $userGroups = array_merge($groupIds, $userGroupsWithTag);

            $query->withWhereHas('steps', function ($step) use ($userGroups, $userId) {
                $step->where(function ($s) use ($userGroups, $userId) {
                    if (!empty($userGroups)) {
                        $s->whereHas('userGroups', function ($g) use ($userGroups) {
                            $g->whereIn('user_groups.id', $userGroups);
                        });
                    }

                    // match direct user access
                    $s->orWhereHas('users', function ($u) use ($userId) {
                        $u->where('users.id', $userId);
                    });
                });
            });
        }

        if ($procedureRequestDTO->searchKey) {
            $query->where(function ($q) use ($procedureRequestDTO) {
                $q->where('title', 'like', '%'.$procedureRequestDTO->searchKey.'%')
                    ->orWhere('description', 'like', '%'.$procedureRequestDTO->searchKey.'%')
                    ->orWhereHas('steps', function ($q) use ($procedureRequestDTO) {
                        $q->where('description', 'like', '%'.$procedureRequestDTO->searchKey.'%');
                    });
            });
        }

        // --- ACTIVE: has at least one active step now ---
        if ($procedureRequestDTO->activePeriod === true) {
            $query->whereHas('steps', function ($q) use ($now) {
                $q->where('starts_at', '<=', $now)
                    ->where('ends_at', '>=', $now);
            });
        }

        // --- EXPIRED: has NO active steps now ---
        // This includes procedures with NO steps. If you want to exclude those in future we can add: $query->whereHas('steps');
        if ($procedureRequestDTO->activePeriod === false) {
            $query->whereDoesntHave('steps', function ($q) use ($now) {
                $q->where('starts_at', '<=', $now)
                    ->where('ends_at', '>=', $now);
            });
        }

        if ($procedureRequestDTO->orderBy === 'starts_at') {
            $desc = $procedureRequestDTO->order !== 'asc';

            // Rank steps per procedure by MOST RECENT (DESC) so we can order by that "latest" step.
            $ranked = ProcedureStep::selectRaw(
                'procedureId, starts_at, ends_at,'.
                'ROW_NUMBER() OVER ('.
                '  PARTITION BY procedureId '.'  ORDER BY starts_at DESC, ends_at DESC'.
                ') AS rn'
            );

            $query->leftJoinSub($ranked, 'r', function ($j) {
                $j->on('r.procedureId', '=', 'procedures.id')
                    ->where('r.rn', 1);
            })
                ->select('procedures.*');

            if ($desc) {
                $query->orderByDesc('r.starts_at')
                    ->orderByDesc('r.ends_at');
            } else {
                $query->orderBy('r.starts_at')
                    ->orderBy('r.ends_at');
            }
        } else {
            $query->{$procedureRequestDTO->order === 'asc' ? 'orderBy' : 'orderByDesc'}($procedureRequestDTO->orderBy);
        }

        return $query->paginate($procedureRequestDTO->rpp);
    }

    public function deleteProcedure(int $procedureId): void
    {
        Procedure::find($procedureId)
            ->update(['isDeleted' => 1]);
    }

    public function upsert(array $data): Procedure
    {
        return DB::transaction(function () use ($data) {
            $procedure = isset($data['id']) ? Procedure::findOrFail($data['id']) : new Procedure();

            // fill everything except nested steps
            $procedure->fill(Arr::except($data, ['steps']))
                ->save();

            // replace steps completely (simple & predictable)
            $procedure->steps()
                ->delete();

            foreach (($data['steps'] ?? []) as $stepPayload) {
                $userGroupIds = $stepPayload['userGroupIds'] ?? [];
                $userIds = $stepPayload['userIds'] ?? [];

                // only the columns that belong to ProcedureStep
                $attrs = Arr::only($stepPayload, ['starts_at', 'ends_at', 'description', 'type']);

                // FK 'procedureId' is auto-set via the hasMany relation
                $step = $procedure->steps()
                    ->create($attrs);

                // attach pivots
                if (!empty($userGroupIds)) {
                    $step->userGroups()
                        ->sync($userGroupIds);
                }
                if (!empty($userIds)) {
                    $step->users()
                        ->sync($userIds);
                }
            }

            // return with nested relations if you need them
            return $procedure->load(['steps.userGroups', 'steps.users']);
        });
    }

    public function massDelete(array $ids): void
    {
        Procedure::whereIn('id', $ids)
            ->update(['isDeleted' => 1]);
    }
}
