{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix"}, "devDependencies": {"@eslint/js": "^9.19.0", "@iconify/vue": "^4.3.0", "@tailwindcss/vite": "^4.0.0", "@types/dropzone": "^5.7.9", "@vitejs/plugin-vue": "^6.0.0", "@vue/eslint-config-typescript": "^14.3.0", "axios": "^1.8.2", "concurrently": "^9.0.1", "eslint-config-prettier": "^10.0.1", "eslint-plugin-vue": "^9.32.0", "laravel-vite-plugin": "^1.3.0", "prettier": "^3.4.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "sass": "1.77.6", "tailwindcss": "^4.0.0", "typescript-eslint": "^8.23.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^8.0.0", "vue": "^3.5.17", "vue-tsc": "^2.2.4"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.4.2", "@ckeditor/ckeditor5-vue": "^7.3.0", "@inertiajs/vue3": "^2.0.15", "@vuepic/vue-datepicker": "^11.0.2", "@vueuse/core": "^13.1.0", "bootstrap": "^5.3.5", "bootstrap-vue-next": "^0.30.5", "choices.js": "^11.1.0", "dropzone": "^6.0.0-beta.2", "flatpickr": "^4.6.13", "pinia": "^3.0.2", "simplebar": "^6.3.0", "simplebar-vue": "^2.4.0", "sweetalert2": "^11.19.1", "typescript": "^5.2.2", "vue-i18n": "^11.1.11", "vuedraggable": "^4.1.0", "ziggy-js": "^2.4.2"}}