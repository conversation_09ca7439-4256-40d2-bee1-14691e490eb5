/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    BBadge: typeof import('bootstrap-vue-next/components/BBadge')['BBadge']
    BButton: typeof import('bootstrap-vue-next/components/BButton')['BButton']
    BCard: typeof import('bootstrap-vue-next/components/BCard')['BCard']
    BCardBody: typeof import('bootstrap-vue-next/components/BCard')['BCardBody']
    BCardFooter: typeof import('bootstrap-vue-next/components/BCard')['BCardFooter']
    BCardTitle: typeof import('bootstrap-vue-next/components/BCard')['BCardTitle']
    BCol: typeof import('bootstrap-vue-next/components/BContainer')['BCol']
    BCollapse: typeof import('bootstrap-vue-next/components/BCollapse')['BCollapse']
    BDropdown: typeof import('bootstrap-vue-next/components/BDropdown')['BDropdown']
    BDropdownItem: typeof import('bootstrap-vue-next/components/BDropdown')['BDropdownItem']
    BForm: typeof import('bootstrap-vue-next/components/BForm')['BForm']
    BFormInput: typeof import('bootstrap-vue-next/components/BFormInput')['BFormInput']
    BFormTextarea: typeof import('bootstrap-vue-next/components/BFormTextarea')['BFormTextarea']
    BModal: typeof import('bootstrap-vue-next/components/BModal')['BModal']
    BOffcanvas: typeof import('bootstrap-vue-next/components/BOffcanvas')['BOffcanvas']
    BRow: typeof import('bootstrap-vue-next/components/BContainer')['BRow']
    BTab: typeof import('bootstrap-vue-next/components/BTabs')['BTab']
    BTabs: typeof import('bootstrap-vue-next/components/BTabs')['BTabs']
    BTh: typeof import('bootstrap-vue-next/components/BTable')['BTh']
  }
  export interface GlobalDirectives {
    vBToggle: typeof import('bootstrap-vue-next/directives/BToggle')['vBToggle']
    vBTooltip: typeof import('bootstrap-vue-next/directives/BTooltip')['vBTooltip']
  }
}
